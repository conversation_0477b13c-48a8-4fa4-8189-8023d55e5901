interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
  sourceStartTime?: number;
  sourceEndTime?: number;
}

interface ZoomEffect {
  id: string;
  startTime: number;
  endTime: number;
  zoomLevel: number;
  clipId: string;
}

interface ExportOptions {
  format: string;
  resolution: string;
  quality: string;
  framerate: number;
}

self.onmessage = async (e) => {
  const { videoClips, zoomEffects, options, timelineStart, timelineDuration } = e.data;
  
  try {
    console.log('=== WORKER EXPORT STARTED ===');
    console.log('Timeline duration:', timelineDuration);
    console.log('Timeline start:', timelineStart);

    const { width, height } = getCanvasDimensions(options.resolution);
    const canvas = new OffscreenCanvas(width, height);
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Failed to get 2D context from OffscreenCanvas');
    }

    const totalFrames = Math.ceil(timelineDuration * options.framerate);
    const frames: ImageBitmap[] = [];

    // Generate frames
    for (let frameIndex = 0; frameIndex < totalFrames; frameIndex++) {
      const currentTime = timelineStart + (frameIndex / options.framerate);
      
      // Clear canvas with black background
      ctx.fillStyle = '#000000';
      ctx.fillRect(0, 0, width, height);

      // Find active clip at current time
      const activeClip = videoClips.find((clip: any) => 
        currentTime >= clip.startTime && currentTime < clip.endTime
      );

      if (activeClip) {
        // Calculate zoom level from effects
        const zoomLevel = getCurrentZoomLevel(currentTime, activeClip.id, zoomEffects);
        const scale = zoomLevel / 100;
        
        // Apply zoom transformation
        ctx.save();
        const offsetX = (width * (1 - scale)) / 2;
        const offsetY = (height * (1 - scale)) / 2;
        
        ctx.translate(offsetX, offsetY);
        ctx.scale(scale, scale);

        // Draw placeholder rectangle for video content
        ctx.fillStyle = '#333333';
        ctx.fillRect(0, 0, width, height);
        
        // Draw clip info text
        ctx.fillStyle = '#ffffff';
        ctx.font = '24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(
          `${activeClip.name}`,
          width / 2,
          height / 2 - 20
        );
        ctx.fillText(
          `Time: ${Math.floor(currentTime)}s`,
          width / 2,
          height / 2 + 20
        );
        ctx.fillText(
          `Zoom: ${zoomLevel}%`,
          width / 2,
          height / 2 + 60
        );
        
        ctx.restore();
      }

      // Create ImageBitmap from canvas
      const imageBitmap = canvas.transferToImageBitmap();
      frames.push(imageBitmap);

      // Report progress
      const progress = ((frameIndex + 1) / totalFrames) * 100;
      self.postMessage({
        type: 'progress',
        progress: Math.min(progress, 100)
      });

      // Add small delay to prevent blocking
      if (frameIndex % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }

    console.log('=== WORKER FRAMES GENERATED ===');
    console.log('Total frames:', frames.length);

    // Send frames back to main thread with proper transfer
    self.postMessage({
      type: 'frames',
      frames: frames
    }, { transfer: frames });

  } catch (error) {
    console.error('Worker error:', error);
    self.postMessage({
      type: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

function getCurrentZoomLevel(currentTime: number, clipId: string, zoomEffects: any[]): number {
  const activeZoomEffect = zoomEffects.find((effect: any) => 
    effect.clipId === clipId &&
    currentTime >= effect.startTime && 
    currentTime <= effect.endTime
  );
  return activeZoomEffect ? activeZoomEffect.zoomLevel : 100;
}

function getCanvasDimensions(resolution: string): { width: number; height: number } {
  switch (resolution) {
    case '720p': return { width: 1280, height: 720 };
    case '1080p': return { width: 1920, height: 1080 };
    case '1440p': return { width: 2560, height: 1440 };
    case '4k': return { width: 3840, height: 2160 };
    default: return { width: 1280, height: 720 };
  }
}
