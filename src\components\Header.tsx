import React, { useState } from 'react';
import { Save, <PERSON>tings, User, ChevronDown, Wifi, WifiOff, Undo, Redo, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
interface HeaderProps {
  currentProject?: {
    name: string;
  } | null;
  isSaving: boolean;
  canUndo: boolean;
  canRedo: boolean;
  onSave: () => void;
  onUndo: () => void;
  onRedo: () => void;
  onExport: () => void;
  videoClipsCount: number;
}
export const Header: React.FC<HeaderProps> = ({
  currentProject,
  isSaving,
  canUndo,
  canRedo,
  onSave,
  onUndo,
  onRedo,
  onExport,
  videoClipsCount
}) => {
  const [activeTab, setActiveTab] = useState('main');
  const [isOnline] = useState(true); // Could be connected to actual network status

  return <div className="h-16 bg-gradient-to-r from-gray-900 to-gray-800 border-b border-gray-700 flex items-center justify-between px-6 shadow-lg py-[5px]">
      {/* Left Section - Logo & Branding */}
      <div className="flex items-center gap-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">VE</span>
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">Video Editor Pro</h1>
          </div>
        </div>

        {/* Project Tabs */}
        <div className="ml-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="bg-gray-800 border-gray-600">
              <TabsTrigger value="main" className="data-[state=active]:bg-gray-700">
                {currentProject?.name || 'Untitled Project'}
                {isSaving && <Badge variant="secondary" className="ml-2 text-xs">Saving...</Badge>}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Center Section - Project Status */}
      <div className="flex items-center gap-4">
        <div className="text-center">
          
          
        </div>
        
        <div className="h-8 w-px bg-gray-600"></div>
        
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-xs text-gray-400">Auto-save enabled</span>
        </div>
      </div>

      {/* Right Section - Actions & Profile */}
      <div className="flex items-center gap-3">
        {/* Undo/Redo */}
        <div className="flex items-center gap-1 bg-gray-800 rounded-lg p-1">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-300 hover:text-white hover:bg-gray-700" onClick={onUndo} disabled={!canUndo} title="Undo (Ctrl+Z)">
            <Undo className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-300 hover:text-white hover:bg-gray-700" onClick={onRedo} disabled={!canRedo} title="Redo (Ctrl+Y)">
            <Redo className="w-4 h-4" />
          </Button>
        </div>

        {/* Save Button */}
        <Button variant="outline" size="sm" className="bg-gray-800 border-gray-600 text-white hover:bg-gray-700 px-4" onClick={onSave} disabled={isSaving}>
          <Save className="w-4 h-4 mr-2" />
          {isSaving ? 'Saving...' : 'Save'}
        </Button>

        {/* Export Button */}
        <Button size="sm" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-4" onClick={onExport} disabled={videoClipsCount === 0}>
          <Download className="w-4 h-4 mr-2" />
          Export
        </Button>

        {/* Divider */}
        <div className="h-8 w-px bg-gray-600"></div>

        {/* User Profile Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="flex items-center gap-2 text-gray-300 hover:text-white hover:bg-gray-700 px-3">
              <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <User className="w-3 h-3 text-white" />
              </div>
              <span className="text-sm">User</span>
              <ChevronDown className="w-3 h-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 bg-gray-800 border-gray-700">
            <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-gray-700">
              <User className="w-4 h-4 mr-2" />
              Profile Settings
            </DropdownMenuItem>
            <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-gray-700">
              <Settings className="w-4 h-4 mr-2" />
              Preferences
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-gray-700" />
            <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-gray-700">
              About Video Editor Pro
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>;
};