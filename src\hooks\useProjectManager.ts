
import { useState, useCallback, useEffect } from 'react';

export interface ProjectState {
  id: string;
  name: string;
  videoClips: any[];
  zoomEffects: any[];
  createdAt: string;
  updatedAt: string;
}

export const useProjectManager = () => {
  const [currentProject, setCurrentProject] = useState<ProjectState | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Load project from localStorage on mount
  useEffect(() => {
    const savedProject = localStorage.getItem('videoEditor_currentProject');
    if (savedProject) {
      try {
        const project = JSON.parse(savedProject);
        setCurrentProject(project);
      } catch (error) {
        console.error('Error loading project:', error);
      }
    }
  }, []);

  const saveProject = useCallback(async (videoClips: any[], zoomEffects: any[], projectName?: string) => {
    setIsSaving(true);
    
    try {
      const now = new Date().toISOString();
      const project: ProjectState = {
        id: currentProject?.id || `project_${Date.now()}`,
        name: projectName || currentProject?.name || 'Untitled Project',
        videoClips,
        zoomEffects,
        createdAt: currentProject?.createdAt || now,
        updatedAt: now,
      };

      localStorage.setItem('videoEditor_currentProject', JSON.stringify(project));
      setCurrentProject(project);
      
      // Also save to projects list
      const savedProjects = localStorage.getItem('videoEditor_projects');
      const projects = savedProjects ? JSON.parse(savedProjects) : [];
      
      const existingIndex = projects.findIndex((p: ProjectState) => p.id === project.id);
      if (existingIndex >= 0) {
        projects[existingIndex] = project;
      } else {
        projects.push(project);
      }
      
      localStorage.setItem('videoEditor_projects', JSON.stringify(projects));
      return project;
    } catch (error) {
      console.error('Error saving project:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [currentProject]);

  const loadProject = useCallback((projectId: string) => {
    const savedProjects = localStorage.getItem('videoEditor_projects');
    if (savedProjects) {
      const projects = JSON.parse(savedProjects);
      const project = projects.find((p: ProjectState) => p.id === projectId);
      if (project) {
        setCurrentProject(project);
        localStorage.setItem('videoEditor_currentProject', JSON.stringify(project));
        return project;
      }
    }
    return null;
  }, []);

  const getAllProjects = useCallback(() => {
    const savedProjects = localStorage.getItem('videoEditor_projects');
    return savedProjects ? JSON.parse(savedProjects) : [];
  }, []);

  return {
    currentProject,
    isSaving,
    saveProject,
    loadProject,
    getAllProjects,
  };
};
