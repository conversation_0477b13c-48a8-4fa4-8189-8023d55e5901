
import { ExportOptions } from '@/components/ExportDialog';
import { VideoExportManager } from './videoExportManager';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
  sourceStartTime?: number;
  sourceEndTime?: number;
}

interface ZoomEffect {
  id: string;
  startTime: number;
  endTime: number;
  zoomLevel: number;
  clipId: string;
}

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  color: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
}

export class VideoExporter {
  private exportManager: VideoExportManager;

  constructor() {
    this.exportManager = new VideoExportManager();
  }

  async exportVideo(
    videoClips: VideoClip[],
    zoomEffects: ZoomEffect[],
    subtitles: Subtitle[],
    options: ExportOptions,
    onProgress: (progress: number) => void
  ): Promise<Blob> {
    return this.exportManager.exportVideo(videoClips, zoomEffects, subtitles, options, onProgress);
  }

  downloadBlob(blob: Blob, filename: string) {
    this.exportManager.downloadBlob(blob, filename);
  }

  cleanup() {
    // Cleanup is handled by export manager
  }
}
