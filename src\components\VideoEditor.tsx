import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Upload, Play, Pause, Square, SkipBack, SkipForward, Volume2, Scissors, Copy, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Timeline } from './Timeline';
import { VideoPreview } from './VideoPreview';
import { ExportDialog, ExportOptions } from './ExportDialog';
import { Header } from './Header';
import { SubtitleEditor } from './SubtitleEditor';
import { SubtitleOverlay } from './SubtitleOverlay';
import { useProjectManager } from '@/hooks/useProjectManager';
import { MediaBunnyExporter } from '@/utils/mediaBunnyExporter';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import { useUndoRedo } from '@/hooks/useUndoRedo';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
  sourceStartTime?: number;
  sourceEndTime?: number;
  muted?: boolean;
}

interface ZoomEffect {
  id: string;
  startTime: number;
  endTime: number;
  zoomLevel: number;
  clipId: string;
}

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
  wordsPerChunk?: number; // For pasted subtitles with chunking
}

// Utility function for formatting time
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

export const VideoEditor = () => {
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState([100]);
  const [videoZoom, setVideoZoom] = useState(100);
  const [timelineZoom, setTimelineZoom] = useState(100);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [copiedClip, setCopiedClip] = useState<VideoClip | null>(null);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [canvasSize, setCanvasSize] = useState<'16:9' | '9:16' | '1:1' | '4:3'>('16:9');

  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Undo/Redo state
  interface EditorState {
    videoClips: VideoClip[];
    zoomEffects: ZoomEffect[];
    selectedClips: string[];
    subtitles: Subtitle[];
    selectedSubtitles: string[];
  }

  const initialEditorState: EditorState = {
    videoClips: [],
    zoomEffects: [],
    selectedClips: [],
    subtitles: [],
    selectedSubtitles: []
  };

  const [editorState, undoRedoActions] = useUndoRedo(initialEditorState);

  // Use the undo/redo state directly instead of separate state variables
  const videoClips = editorState.videoClips;
  const zoomEffects = editorState.zoomEffects;
  const selectedClips = editorState.selectedClips;
  const subtitles = editorState.subtitles;
  const selectedSubtitles = editorState.selectedSubtitles;

  // Get the first selected clip or null
  const selectedClip = selectedClips.length > 0 ? selectedClips[0] : null;

  // Function to update editor state with undo/redo support
  const updateEditorState = useCallback((updates: Partial<EditorState>) => {
    const newState = {
      ...editorState,
      ...updates
    };
    undoRedoActions.executeAction(newState);
  }, [editorState, undoRedoActions]);

  // Calculate timeline duration instead of using original video duration
  const getTimelineDuration = useCallback(() => {
    if (videoClips.length === 0) return 0;
    
    // Find the earliest start and latest end time
    const earliestStart = Math.min(...videoClips.map(clip => clip.startTime));
    const latestEnd = Math.max(...videoClips.map(clip => clip.endTime));
    
    return latestEnd - earliestStart;
  }, [videoClips]);

  // Get timeline start offset
  const getTimelineStart = useCallback(() => {
    if (videoClips.length === 0) return 0;
    return Math.min(...videoClips.map(clip => clip.startTime));
  }, [videoClips]);

  // Updated function to find which clip should be playing at current time
  const getCurrentPlayingClip = useCallback(() => {
    return videoClips.find(clip => 
      currentTime >= clip.startTime && currentTime < clip.endTime
    );
  }, [videoClips, currentTime]);

  // Get current clip for video preview
  const currentClip = getCurrentPlayingClip();

  // Calculate current zoom level based on active zoom effects
  const getCurrentZoomLevel = useCallback(() => {
    const currentPlayingClip = getCurrentPlayingClip();
    if (!currentPlayingClip) return 100;
    
    const activeZoomEffect = zoomEffects.find(effect => 
      effect.clipId === currentPlayingClip.id &&
      currentTime >= effect.startTime && 
      currentTime <= effect.endTime
    );
    return activeZoomEffect ? activeZoomEffect.zoomLevel : 100;
  }, [zoomEffects, currentTime, getCurrentPlayingClip]);

  // Enhanced handlers with undo/redo support
  const handleClipSelectWithHistory = useCallback((clipId: string, isMultiSelect?: boolean) => {
    let newSelectedClips: string[];
    if (isMultiSelect) {
      if (selectedClips.includes(clipId)) {
        newSelectedClips = selectedClips.filter(id => id !== clipId);
      } else {
        newSelectedClips = [...selectedClips, clipId];
      }
    } else {
      newSelectedClips = [clipId];
    }
    
    updateEditorState({ 
      selectedClips: newSelectedClips,
      selectedSubtitles: [] // Clear subtitle selection when clip is selected
    });
  }, [selectedClips, updateEditorState]);

  const handleClipTrimWithHistory = useCallback((clipId: string, newStartTime: number, newEndTime: number) => {
    const updatedClips = videoClips.map(clip => {
      if (clip.id === clipId) {
        const clipDuration = clip.endTime - clip.startTime;
        const newClipDuration = newEndTime - newStartTime;
        const trimmedFromStart = newStartTime - clip.startTime;
        const trimmedFromEnd = clip.endTime - newEndTime;
        
        const originalSourceStart = clip.sourceStartTime || 0;
        const originalSourceEnd = clip.sourceEndTime || clip.duration;
        const sourceDuration = originalSourceEnd - originalSourceStart;
        
        const newSourceStart = originalSourceStart + (trimmedFromStart / clipDuration) * sourceDuration;
        const newSourceEnd = originalSourceEnd - (trimmedFromEnd / clipDuration) * sourceDuration;
        
        return {
          ...clip,
          startTime: newStartTime,
          endTime: newEndTime,
          sourceStartTime: newSourceStart,
          sourceEndTime: newSourceEnd
        };
      }
      return clip;
    });
    
    const updatedZoomEffects = zoomEffects.map(effect => {
      if (effect.clipId === clipId) {
        const constrainedStart = Math.max(newStartTime, effect.startTime);
        const constrainedEnd = Math.min(newEndTime, effect.endTime);
        
        if (constrainedStart < constrainedEnd) {
          return {
            ...effect,
            startTime: constrainedStart,
            endTime: constrainedEnd
          };
        }
      }
      return effect;
    }).filter(effect => {
      if (effect.clipId === clipId) {
        return effect.startTime < newEndTime && effect.endTime > newStartTime;
      }
      return true;
    });
    
    updateEditorState({ 
      videoClips: updatedClips, 
      zoomEffects: updatedZoomEffects 
    });
    
    toast.success(`Clip trimmed to ${Math.floor(newEndTime - newStartTime)}s duration`);
  }, [videoClips, zoomEffects, updateEditorState]);

  const handleClipMoveWithHistory = useCallback((clipId: string, newStartTime: number) => {
    const updatedClips = videoClips.map(clip => {
      if (clip.id === clipId) {
        const clipDuration = clip.endTime - clip.startTime;
        return {
          ...clip,
          startTime: newStartTime,
          endTime: newStartTime + clipDuration
        };
      }
      return clip;
    });
    
    const updatedZoomEffects = zoomEffects.map(effect => {
      if (effect.clipId === clipId) {
        const clip = videoClips.find(c => c.id === clipId);
        if (clip) {
          const timeDiff = newStartTime - clip.startTime;
          return {
            ...effect,
            startTime: effect.startTime + timeDiff,
            endTime: effect.endTime + timeDiff
          };
        }
      }
      return effect;
    });
    
    updateEditorState({ 
      videoClips: updatedClips, 
      zoomEffects: updatedZoomEffects 
    });
  }, [videoClips, zoomEffects, updateEditorState]);

  const handleClipMute = useCallback((clipId: string, muted: boolean) => {
    const updatedClips = videoClips.map(clip => {
      if (clip.id === clipId) {
        return {
          ...clip,
          muted
        };
      }
      return clip;
    });
    updateEditorState({ videoClips: updatedClips });
  }, [videoClips, updateEditorState]);

  // Enhanced delete with undo/redo
  const handleDeleteClipWithHistory = useCallback(() => {
    if (selectedClips.length === 0) {
      toast.error('Please select video clips first');
      return;
    }

    const currentPlayingClip = getCurrentPlayingClip();
    if (currentPlayingClip && selectedClips.includes(currentPlayingClip.id)) {
      setIsPlaying(false);
      if (videoRef.current) {
        videoRef.current.pause();
      }
    }

    const updatedClips = videoClips.filter(clip => !selectedClips.includes(clip.id));
    const updatedZoomEffects = zoomEffects.filter(effect => !selectedClips.includes(effect.clipId));
    
    updateEditorState({ 
      videoClips: updatedClips, 
      zoomEffects: updatedZoomEffects,
      selectedClips: [] 
    });
    
    if (currentPlayingClip && selectedClips.includes(currentPlayingClip.id)) {
      const nextClip = updatedClips.find(clip => clip.startTime >= currentTime);
      setCurrentTime(nextClip ? nextClip.startTime : 0);
    }
    
    toast.success(`${selectedClips.length} clip${selectedClips.length > 1 ? 's' : ''} deleted`);
  }, [selectedClips, videoClips, getCurrentPlayingClip, zoomEffects, currentTime, updateEditorState]);

  // Select all function
  const handleSelectAll = useCallback(() => {
    updateEditorState({ selectedClips: videoClips.map(clip => clip.id) });
    toast.success(`Selected all ${videoClips.length} clips`);
  }, [videoClips, updateEditorState]);

  const handleFileUpload = useCallback((files: FileList | null) => {
    if (!files) return;
    
    const file = files[0];
    const supportedFormats = ['video/mp4', 'video/mov', 'video/avi', 'video/webm'];
    
    if (!supportedFormats.includes(file.type)) {
      toast.error('Unsupported video format. Please use MP4, MOV, AVI, or WebM.');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          setIsUploading(false);
          
          const url = URL.createObjectURL(file);
          const video = document.createElement('video');
          video.src = url;
          
          video.onloadedmetadata = () => {
            const newClip: VideoClip = {
              id: Date.now().toString(),
              name: file.name,
              duration: video.duration,
              startTime: 0,
              endTime: video.duration,
              file,
              url,
              sourceStartTime: 0,
              sourceEndTime: video.duration
            };
            
            updateEditorState({ 
              videoClips: [...videoClips, newClip],
              selectedClips: [newClip.id] 
            });
            toast.success(`Video "${file.name}" uploaded successfully!`);
          };
          
          return 100;
        }
        return prev + 10;
      });
    }, 100);
  }, [videoClips, updateEditorState]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    handleFileUpload(e.dataTransfer.files);
  }, [handleFileUpload]);

  const togglePlayPause = useCallback(() => {
    const currentPlayingClip = getCurrentPlayingClip();
    if (!currentPlayingClip || !videoRef.current) {
      console.log('No clip to play or video ref not available');
      return;
    }

    try {
      // Calculate the time within the original video file
      const relativeTimeInTimeline = currentTime - currentPlayingClip.startTime;
      const sourceStartTime = currentPlayingClip.sourceStartTime || 0;
      const clipDuration = currentPlayingClip.endTime - currentPlayingClip.startTime;
      const sourceDuration = (currentPlayingClip.sourceEndTime || currentPlayingClip.duration) - sourceStartTime;
      
      // Map timeline position to source video position proportionally
      const videoSourceTime = sourceStartTime + (relativeTimeInTimeline / clipDuration) * sourceDuration;
      
      console.log('Setting video time to:', videoSourceTime, 'for timeline time:', currentTime);
      
      videoRef.current.currentTime = Math.max(0, Math.min(videoSourceTime, currentPlayingClip.duration));
      
      if (isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      } else {
        const playPromise = videoRef.current.play();
        if (playPromise) {
          playPromise
            .then(() => {
              setIsPlaying(true);
              console.log('Video playing successfully');
            })
            .catch((error) => {
              console.error('Failed to play video:', error);
              setIsPlaying(false);
            });
        } else {
          setIsPlaying(true);
        }
      }
    } catch (error) {
      console.error('Error in togglePlayPause:', error);
      setIsPlaying(false);
    }
  }, [isPlaying, getCurrentPlayingClip, currentTime]);

  const handleTimeUpdate = useCallback(() => {
    if (!videoRef.current) return;
    
    const currentPlayingClip = getCurrentPlayingClip();
    if (!currentPlayingClip) return;

    try {
      const sourceStartTime = currentPlayingClip.sourceStartTime || 0;
      const videoSourceTime = videoRef.current.currentTime;
      const clipDuration = currentPlayingClip.endTime - currentPlayingClip.startTime;
      const sourceDuration = (currentPlayingClip.sourceEndTime || currentPlayingClip.duration) - sourceStartTime;
      
      // Map source video time back to timeline time proportionally
      const relativeSourceTime = videoSourceTime - sourceStartTime;
      const relativeTimeInTimeline = (relativeSourceTime / sourceDuration) * clipDuration;
      const globalTime = currentPlayingClip.startTime + relativeTimeInTimeline;
      
      // Check if we've reached the end of the current clip
      if (globalTime >= currentPlayingClip.endTime) {
        // Find next clip
        const nextClip = videoClips.find(clip => clip.startTime >= currentPlayingClip.endTime);
        if (nextClip) {
          // Jump to next clip
          setCurrentTime(nextClip.startTime);
          const nextSourceStart = nextClip.sourceStartTime || 0;
          videoRef.current.currentTime = nextSourceStart;
        } else {
          // No more clips, stop playback
          setIsPlaying(false);
          videoRef.current.pause();
        }
      } else {
        setCurrentTime(globalTime);
      }
      
      // Update video zoom based on current zoom effects
      const currentZoom = getCurrentZoomLevel();
      setVideoZoom(currentZoom);
    } catch (error) {
      console.error('Error in handleTimeUpdate:', error);
    }
  }, [getCurrentPlayingClip, videoClips, getCurrentZoomLevel]);

  const handleSeek = useCallback((time: number) => {
    // Constrain time to timeline bounds
    const timelineStart = getTimelineStart();
    const timelineDuration = getTimelineDuration();
    const constrainedTime = Math.max(timelineStart, Math.min(timelineStart + timelineDuration, time));
    
    setCurrentTime(constrainedTime);
    
    // Find which clip should be playing at this time
    const targetClip = videoClips.find(clip => 
      constrainedTime >= clip.startTime && constrainedTime < clip.endTime
    );
    
    if (targetClip && videoRef.current) {
      try {
        // Calculate relative time within the target clip
        const relativeTimeInTimeline = constrainedTime - targetClip.startTime;
        const sourceStartTime = targetClip.sourceStartTime || 0;
        const clipDuration = targetClip.endTime - targetClip.startTime;
        const sourceDuration = (targetClip.sourceEndTime || targetClip.duration) - sourceStartTime;
        
        // Map timeline position to source video position proportionally
        const videoSourceTime = sourceStartTime + (relativeTimeInTimeline / clipDuration) * sourceDuration;
        
        videoRef.current.currentTime = Math.max(0, Math.min(videoSourceTime, targetClip.duration));
        
        // Only auto-select clip if no subtitles are currently selected
        if (selectedClip !== targetClip.id && selectedSubtitles.length === 0) {
          updateEditorState({ selectedClips: [targetClip.id] });
        }
      } catch (error) {
        console.error('Error in handleSeek:', error);
      }
    }
    
    // Update video zoom when seeking
    const currentZoom = getCurrentZoomLevel();
    setVideoZoom(currentZoom);
  }, [videoClips, selectedClip, getCurrentZoomLevel, getTimelineStart, getTimelineDuration, updateEditorState]);

  const handleVolumeChange = useCallback((value: number[]) => {
    setVolume(value);
    if (videoRef.current) {
      videoRef.current.volume = value[0] / 100;
    }
  }, []);

  // New function to add zoom effect
  const handleAddZoomEffect = useCallback((zoomLevel: number) => {
    if (!selectedClip) {
      toast.error('Please select a video clip first');
      return;
    }

    const currentClipObj = videoClips.find(clip => clip.id === selectedClip);
    if (!currentClipObj) return;

    // Create a 5-second zoom effect starting from current time
    const effectDuration = 5;
    const startTime = currentTime;
    const endTime = Math.min(startTime + effectDuration, currentClipObj.endTime);

    const newZoomEffect: ZoomEffect = {
      id: Date.now().toString(),
      startTime,
      endTime,
      zoomLevel,
      clipId: selectedClip
    };

    updateEditorState({ zoomEffects: [...zoomEffects, newZoomEffect] });
    toast.success(`Zoom effect (${zoomLevel}%) added at ${Math.floor(startTime)}s`);
  }, [selectedClip, currentTime, videoClips, zoomEffects, updateEditorState]);

  const handleVideoZoomChange = useCallback((zoom: number) => {
    // Instead of directly changing zoom, add a zoom effect
    handleAddZoomEffect(zoom);
  }, [handleAddZoomEffect]);

  const handleTimelineSeek = useCallback((time: number) => {
    handleSeek(time);
  }, [handleSeek]);

  const handleTimelineZoomChange = useCallback((zoom: number) => {
    setTimelineZoom(zoom);
  }, []);

  // Handle zoom effect movement
  const handleZoomEffectMove = useCallback((effectId: string, newStartTime: number) => {
    const effect = zoomEffects.find(e => e.id === effectId);
    if (!effect) return;

    const effectDuration = effect.endTime - effect.startTime;
    const newEndTime = newStartTime + effectDuration;

    // Ensure the effect stays within its associated clip bounds
    const associatedClip = videoClips.find(c => c.id === effect.clipId);
    if (associatedClip) {
      const clampedStartTime = Math.max(associatedClip.startTime, Math.min(associatedClip.endTime - effectDuration, newStartTime));
      const clampedEndTime = clampedStartTime + effectDuration;

      const updatedZoomEffects = zoomEffects.map(e => 
        e.id === effectId 
          ? { ...e, startTime: clampedStartTime, endTime: clampedEndTime }
          : e
      );

      updateEditorState({ zoomEffects: updatedZoomEffects });
      toast.success('Zoom effect moved');
    }
  }, [zoomEffects, videoClips, updateEditorState]);

  // Handle zoom effect deletion
  const handleZoomEffectDelete = useCallback((effectId: string) => {
    const updatedZoomEffects = zoomEffects.filter(e => e.id !== effectId);
    updateEditorState({ zoomEffects: updatedZoomEffects });
    toast.success('Zoom effect deleted');
  }, [zoomEffects, updateEditorState]);

  // Handle zoom effect trimming
  const handleZoomEffectTrim = useCallback((effectId: string, startTime: number, endTime: number) => {
    const effect = zoomEffects.find(e => e.id === effectId);
    if (!effect) return;

    // Ensure the effect stays within its associated clip bounds
    const associatedClip = videoClips.find(c => c.id === effect.clipId);
    if (associatedClip) {
      const clampedStartTime = Math.max(associatedClip.startTime, Math.min(associatedClip.endTime, startTime));
      const clampedEndTime = Math.max(clampedStartTime + 0.1, Math.min(associatedClip.endTime, endTime));

      const updatedZoomEffects = zoomEffects.map(e => 
        e.id === effectId 
          ? { ...e, startTime: clampedStartTime, endTime: clampedEndTime }
          : e
      );

      updateEditorState({ zoomEffects: updatedZoomEffects });
      toast.success('Zoom effect trimmed');
    }
  }, [zoomEffects, videoClips, updateEditorState]);

  // Subtitle management functions
  const handleAddSubtitle = useCallback(() => {
    const newSubtitle: Subtitle = {
      id: Date.now().toString(),
      text: 'New Subtitle',
      startTime: currentTime,
      endTime: currentTime + 3, // 3 second default duration
      fontSize: 24,
      fontColor: '#FFFFFF',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      position: 'bottom',
      fontFamily: 'Arial, sans-serif'
    };

    updateEditorState({ 
      subtitles: [...subtitles, newSubtitle],
      selectedSubtitles: [newSubtitle.id]
    });
    toast.success('Subtitle added');
  }, [currentTime, subtitles, updateEditorState]);

  const handleUpdateSubtitle = useCallback((subtitleId: string, updates: Partial<Subtitle>) => {
    const updatedSubtitles = subtitles.map(subtitle => 
      subtitle.id === subtitleId ? { ...subtitle, ...updates } : subtitle
    );
    updateEditorState({ subtitles: updatedSubtitles });
  }, [subtitles, updateEditorState]);

  const handleDeleteSubtitle = useCallback((subtitleId: string) => {
    const updatedSubtitles = subtitles.filter(subtitle => subtitle.id !== subtitleId);
    const updatedSelectedSubtitles = selectedSubtitles.filter(id => id !== subtitleId);
    updateEditorState({ 
      subtitles: updatedSubtitles,
      selectedSubtitles: updatedSelectedSubtitles
    });
    toast.success('Subtitle deleted');
  }, [subtitles, selectedSubtitles, updateEditorState]);

  const handleSubtitleSelect = useCallback((subtitleId: string) => {
    updateEditorState({ 
      selectedSubtitles: [subtitleId],
      selectedClips: [] // Clear clip selection when subtitle is selected
    });
  }, [updateEditorState]);

  const handleSubtitleMove = useCallback((subtitleId: string, newStartTime: number) => {
    const subtitle = subtitles.find(s => s.id === subtitleId);
    if (!subtitle) return;

    const duration = subtitle.endTime - subtitle.startTime;
    const newEndTime = newStartTime + duration;

    handleUpdateSubtitle(subtitleId, {
      startTime: Math.max(0, newStartTime),
      endTime: newEndTime
    });
  }, [subtitles, handleUpdateSubtitle]);

  const handleSubtitleTrim = useCallback((subtitleId: string, startTime: number, endTime: number) => {
    handleUpdateSubtitle(subtitleId, {
      startTime: Math.max(0, startTime),
      endTime: Math.max(startTime + 0.1, endTime)
    });
  }, [handleUpdateSubtitle]);

  const handleImportSubtitles = useCallback((importedSubtitles: Subtitle[]) => {
    updateEditorState({
      subtitles: [...subtitles, ...importedSubtitles],
      selectedSubtitles: importedSubtitles.length > 0 ? [importedSubtitles[0].id] : []
    });
    toast.success(`Imported ${importedSubtitles.length} subtitles from transcript`);
  }, [subtitles, updateEditorState]);

  const handleDeleteAllSubtitles = useCallback(() => {
    updateEditorState({
      subtitles: [],
      selectedSubtitles: []
    });
    toast.success('All subtitles deleted');
  }, [updateEditorState]);

  const handleAddPastedSubtitle = useCallback((text: string, wordsPerChunk: number) => {
    const timelineDuration = getTimelineDuration();
    if (!text.trim() || !timelineDuration) {
      toast.error('Please provide text and ensure video is loaded');
      return;
    }

    // Create a single subtitle that spans the entire video duration
    // The text will be chunked for display but stored as one clip
    const subtitle: Subtitle = {
      id: `pasted_${Date.now()}`,
      text: text.trim(),
      startTime: 0,
      endTime: timelineDuration,
      fontSize: 24,
      fontColor: '#FFFFFF',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      position: 'bottom',
      fontFamily: 'Arial, sans-serif',
      // Store chunking info for display purposes
      wordsPerChunk: wordsPerChunk
    };
    
    updateEditorState({
      subtitles: [...subtitles, subtitle],
      selectedSubtitles: [subtitle.id]
    });
    
    const words = text.trim().split(/\s+/);
    const totalChunks = Math.ceil(words.length / wordsPerChunk);
    toast.success(`Added subtitle clip with ${totalChunks} chunks (${wordsPerChunk} words each)`);
  }, [getTimelineDuration, subtitles, updateEditorState]);

  // Get current active subtitle
  const getCurrentSubtitle = useCallback(() => {
    return subtitles.find(subtitle =>
      currentTime >= subtitle.startTime && currentTime < subtitle.endTime
    );
  }, [subtitles, currentTime]);

  // Cut Tool: Split clip at current playhead position
  const handleCutClip = useCallback(() => {
    if (selectedClips.length === 0) {
      toast.error('Please select a video clip first');
      return;
    }

    const currentPlayingClip = getCurrentPlayingClip();
    if (!currentPlayingClip || !selectedClips.includes(currentPlayingClip.id)) {
      toast.error('Selected clip is not playing at current time');
      return;
    }

    // Check if current time is within the clip's range
    if (currentTime <= currentPlayingClip.startTime || currentTime >= currentPlayingClip.endTime) {
      toast.error('Cannot cut at the beginning or end of a clip');
      return;
    }

    // Pause playback during cut operation
    if (isPlaying) {
      setIsPlaying(false);
      if (videoRef.current) {
        videoRef.current.pause();
      }
    }

    // Calculate the relative position within the clip
    const relativeTimeInClip = currentTime - currentPlayingClip.startTime;
    const sourceStartTime = currentPlayingClip.sourceStartTime || 0;
    const sourceCutTime = sourceStartTime + relativeTimeInClip;

    // Create two new clips from the split
    const firstClip: VideoClip = {
      ...currentPlayingClip,
      id: `${currentPlayingClip.id}_split1`,
      name: `${currentPlayingClip.name} (Part 1)`,
      endTime: currentTime,
      sourceStartTime: sourceStartTime,
      sourceEndTime: sourceCutTime
    };

    const secondClip: VideoClip = {
      ...currentPlayingClip,
      id: `${currentPlayingClip.id}_split2`, 
      name: `${currentPlayingClip.name} (Part 2)`,
      startTime: currentTime,
      sourceStartTime: sourceCutTime,
      sourceEndTime: currentPlayingClip.sourceEndTime
    };

    // Update clips array
    const updatedClips = videoClips.map(clip => 
      clip.id === currentPlayingClip.id ? firstClip : clip
    ).concat(secondClip);

    // Update zoom effects to match the new clip IDs
    const updatedZoomEffects = zoomEffects.map(effect => {
      if (effect.clipId === currentPlayingClip.id) {
        if (effect.startTime < currentTime) {
          return { ...effect, clipId: firstClip.id };
        } else {
          return { ...effect, clipId: secondClip.id };
        }
      }
      return effect;
    });

    updateEditorState({ 
      videoClips: updatedClips,
      zoomEffects: updatedZoomEffects,
      selectedClips: [firstClip.id] 
    });
    
    toast.success(`Clip cut at ${Math.floor(currentTime)}s`);
  }, [getCurrentPlayingClip, currentTime, isPlaying, selectedClips, videoClips, zoomEffects, updateEditorState]);

  // Copy Tool: Copy selected clip
  const handleCopyClip = useCallback(() => {
    if (selectedClips.length === 0) {
      toast.error('Please select a video clip first');
      return;
    }

    const currentClipObj = videoClips.find(clip => clip.id === selectedClips[0]);
    if (!currentClipObj) return;

    setCopiedClip(currentClipObj);
    toast.success(`${selectedClips.length} clip${selectedClips.length > 1 ? 's' : ''} copied to clipboard`);
  }, [selectedClips, videoClips]);

  // Paste Tool: Paste copied clip
  const handlePasteClip = useCallback(() => {
    if (!copiedClip) {
      toast.error('No clip in clipboard to paste');
      return;
    }

    // Find the next available position after all existing clips
    const maxEndTime = Math.max(...videoClips.map(clip => clip.endTime), 0);
    const clipDuration = copiedClip.endTime - copiedClip.startTime;
    
    const pastedClip: VideoClip = {
      ...copiedClip,
      id: Date.now().toString(),
      name: `${copiedClip.name} (Copy)`,
      startTime: maxEndTime,
      endTime: maxEndTime + clipDuration,
      // Keep the same source times since it's the same video content
      sourceStartTime: copiedClip.sourceStartTime,
      sourceEndTime: copiedClip.sourceEndTime
    };

    // Also copy any zoom effects associated with the original clip
    const associatedZoomEffects = zoomEffects.filter(effect => effect.clipId === copiedClip.id);
    const newZoomEffects = associatedZoomEffects.map(effect => ({
      ...effect,
      id: `${effect.id}_copy_${Date.now()}`,
      clipId: pastedClip.id,
      startTime: effect.startTime - copiedClip.startTime + pastedClip.startTime,
      endTime: effect.endTime - copiedClip.startTime + pastedClip.startTime
    }));

    updateEditorState({ 
      videoClips: [...videoClips, pastedClip],
      zoomEffects: [...zoomEffects, ...newZoomEffects],
      selectedClips: [pastedClip.id] 
    });
    
    toast.success(`Clip "${pastedClip.name}" pasted at ${Math.floor(maxEndTime)}s`);
  }, [copiedClip, videoClips, zoomEffects, updateEditorState]);

  // Delete Tool: Remove selected clip
  const handleDeleteClip = useCallback(() => {
    handleDeleteClipWithHistory();
  }, [handleDeleteClipWithHistory]);

  // Enhanced frame navigation
  const handleFrameBack = useCallback(() => {
    const frameRate = 30; // Assuming 30fps
    const frameTime = 1 / frameRate;
    const newTime = Math.max(getTimelineStart(), currentTime - frameTime);
    handleSeek(newTime);
  }, [currentTime, getTimelineStart, handleSeek]);

  const handleFrameForward = useCallback(() => {
    const frameRate = 30; // Assuming 30fps
    const frameTime = 1 / frameRate;
    const timelineEnd = getTimelineStart() + getTimelineDuration();
    const newTime = Math.min(timelineEnd, currentTime + frameTime);
    handleSeek(newTime);
  }, [currentTime, getTimelineStart, getTimelineDuration, handleSeek]);

  // Enhanced playback rate handling
  const handlePlaybackRateChange = useCallback((rate: number) => {
    setPlaybackRate(rate);
    if (videoRef.current) {
      videoRef.current.playbackRate = rate;
    }
  }, []);

  // Enhanced skip functions
  const handleSkipBackEnhanced = useCallback(() => {
    const timelineStart = getTimelineStart();
    const newTime = Math.max(timelineStart, currentTime - 10);
    handleSeek(newTime);
  }, [currentTime, getTimelineStart, handleSeek]);

  const handleSkipForwardEnhanced = useCallback(() => {
    const timelineEnd = getTimelineStart() + getTimelineDuration();
    const newTime = Math.min(timelineEnd, currentTime + 10);
    handleSeek(newTime);
  }, [currentTime, getTimelineStart, getTimelineDuration, handleSeek]);

  // Handle deleting selected subtitles
  const handleDeleteSelectedSubtitles = useCallback(() => {
    if (selectedSubtitles.length === 0) {
      toast.error('No subtitles selected');
      return;
    }

    const updatedSubtitles = subtitles.filter(subtitle => !selectedSubtitles.includes(subtitle.id));
    updateEditorState({ 
      subtitles: updatedSubtitles,
      selectedSubtitles: []
    });
    toast.success(`${selectedSubtitles.length} subtitle${selectedSubtitles.length > 1 ? 's' : ''} deleted`);
  }, [selectedSubtitles, subtitles, updateEditorState]);

  // Modified delete function to handle both clips and subtitles based on selection
  const handleSmartDelete = useCallback(() => {
    // If subtitles are selected, delete them; otherwise delete clips
    if (selectedSubtitles.length > 0) {
      handleDeleteSelectedSubtitles();
    } else if (selectedClips.length > 0) {
      handleDeleteClipWithHistory();
    } else {
      toast.error('No clips or subtitles selected');
    }
  }, [selectedSubtitles, selectedClips, handleDeleteSelectedSubtitles, handleDeleteClipWithHistory]);

  // Keyboard shortcuts
  useKeyboardShortcuts({
    onPlay: togglePlayPause,
    onCut: handleCutClip,
    onCopy: handleCopyClip,
    onPaste: handlePasteClip,
    onDelete: handleSmartDelete,
    onDeleteSubtitle: handleDeleteSelectedSubtitles,
    onUndo: undoRedoActions.undo,
    onRedo: undoRedoActions.redo,
    onSave: () => handleSaveProject(),
    onSelectAll: handleSelectAll,
    onZoomIn: () => setTimelineZoom(prev => Math.min(400, prev + 25)),
    onZoomOut: () => setTimelineZoom(prev => Math.max(25, prev - 25))
  });

  // Project manager hook
  const { currentProject, isSaving, saveProject } = useProjectManager();

  // New save project function - moved before useEffect
  const handleSaveProject = useCallback(async (projectName?: string) => {
    try {
      await saveProject(videoClips, zoomEffects, projectName);
      toast.success('Project saved successfully!');
    } catch (error) {
      toast.error('Failed to save project');
      console.error('Save error:', error);
    }
  }, [saveProject, videoClips, zoomEffects]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (videoClips.length > 0) {
        handleSaveProject();
      }
    }, 30000); // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval);
  }, [handleSaveProject, videoClips]);

  // Export functionality
  const handleExportVideo = useCallback(async (options: ExportOptions) => {
    if (videoClips.length === 0) {
      toast.error('No video clips to export');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      const exporter = new MediaBunnyExporter();
      const blob = await exporter.exportVideo(
        videoClips,
        zoomEffects,
        subtitles,
        options,
        (progress) => setExportProgress(progress)
      );

      const filename = `${currentProject?.name || 'video'}_export.${options.format}`;
      exporter.downloadBlob(blob, filename);
      
      toast.success('Video exported successfully!');
      setShowExportDialog(false);
    } catch (error) {
      toast.error('Failed to export video');
      console.error('Export error:', error);
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  }, [videoClips, zoomEffects, subtitles, currentProject]);

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-white">
      {/* Modern Header */}
      <Header 
        currentProject={currentProject}
        isSaving={isSaving}
        canUndo={undoRedoActions.canUndo}
        canRedo={undoRedoActions.canRedo}
        onSave={() => handleSaveProject()}
        onUndo={undoRedoActions.undo}
        onRedo={undoRedoActions.redo}
        onExport={() => setShowExportDialog(true)}
        videoClipsCount={videoClips.length}
      />

      {/* Main Content */}
      <div className="h-2/3 flex">
        {/* Left Panel - Enhanced Video Preview */}
        <div className="w-2/3 flex flex-col overflow-hidden">
          <VideoPreview
            currentClip={currentClip}
            videoZoom={videoZoom}
            onVideoZoomChange={(zoom) => {
              // Handle zoom effect addition here if needed
              console.log('Video zoom change:', zoom);
            }}
            onTimeUpdate={handleTimeUpdate}
            onEnded={() => setIsPlaying(false)}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onUploadClick={() => fileInputRef.current?.click()}
            videoRef={videoRef}
            isPlaying={isPlaying}
            currentTime={currentTime}
            volume={volume}
            playbackRate={playbackRate}
            canGoBack={currentTime > getTimelineStart()}
            canGoForward={currentTime < getTimelineStart() + getTimelineDuration()}
            onPlay={() => {
              console.log('Play button clicked');
              if (!isPlaying) togglePlayPause();
            }}
            onPause={() => {
              console.log('Pause button clicked');
              if (isPlaying) togglePlayPause();
            }}
            onSeek={handleSeek}
            onVolumeChange={handleVolumeChange}
            onPlaybackRateChange={setPlaybackRate}
            onSkipBack={() => {
              const timelineStart = getTimelineStart();
              const newTime = Math.max(timelineStart, currentTime - 10);
              handleSeek(newTime);
            }}
            onSkipForward={() => {
              const timelineEnd = getTimelineStart() + getTimelineDuration();
              const newTime = Math.min(timelineEnd, currentTime + 10);
              handleSeek(newTime);
            }}
            onFrameBack={() => {
              const frameRate = 30;
              const frameTime = 1 / frameRate;
              const newTime = Math.max(getTimelineStart(), currentTime - frameTime);
              handleSeek(newTime);
            }}
            onFrameForward={() => {
              const frameRate = 30;
              const frameTime = 1 / frameRate;
              const timelineEnd = getTimelineStart() + getTimelineDuration();
              const newTime = Math.min(timelineEnd, currentTime + frameTime);
              handleSeek(newTime);
            }}
            subtitles={subtitles}
            getCurrentSubtitle={getCurrentSubtitle}
            canvasSize={canvasSize}
          />

          {/* Simplified Playback Controls - Removed duplicate play button */}
          <div className="h-16 bg-gray-800 border-t border-gray-700 flex items-center justify-between px-6">
            <div className="flex items-center gap-4">
              {/* Editing Tools - removed play button */}
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-white hover:bg-gray-700"
                onClick={handleCutClip}
                disabled={selectedClips.length === 0}
                title="Cut clip at current position (C)"
              >
                <Scissors className="w-5 h-5" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-white hover:bg-gray-700"
                onClick={handleCopyClip}
                disabled={selectedClips.length === 0}
                title="Copy selected clip (Ctrl+C)"
              >
                <Copy className="w-5 h-5" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-white hover:bg-gray-700"
                onClick={handleSmartDelete}
                disabled={selectedClips.length === 0 && selectedSubtitles.length === 0}
                title="Delete selected items (Delete)"
              >
                <Trash2 className="w-5 h-5" />
              </Button>
              {copiedClip && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-green-400 hover:bg-gray-700"
                  onClick={handlePasteClip}
                  title={`Paste "${copiedClip.name}" (Ctrl+V)`}
                >
                  📋
                </Button>
              )}
            </div>

            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-400">
                Timeline: {formatTime(currentTime - getTimelineStart())} / {formatTime(getTimelineDuration())}
              </span>
              <span className="text-xs text-gray-500">
                Speed: {playbackRate}x
              </span>
            </div>
          </div>
        </div>

        {/* Right Panel - Tools */}
        <div className="w-1/3 bg-gray-800 border-l border-gray-700 flex flex-col overflow-y-auto">
          {/* Subtitle Editor */}
          <div className="p-4 border-b border-gray-700">
            <SubtitleEditor
              subtitles={subtitles}
              selectedSubtitles={selectedSubtitles}
              currentTime={currentTime}
              videoDuration={getTimelineDuration()}
              onAddSubtitle={handleAddSubtitle}
              onUpdateSubtitle={handleUpdateSubtitle}
              onDeleteSubtitle={handleDeleteSubtitle}
              onSelectSubtitle={handleSubtitleSelect}
              onImportSubtitles={handleImportSubtitles}
              onDeleteAllSubtitles={handleDeleteAllSubtitles}
              onAddPastedSubtitle={handleAddPastedSubtitle}
            />
          </div>

          <div className="p-4 border-b border-gray-700">
            <h3 className="font-semibold text-gray-200 mb-4">Zoom Effects</h3>
            <div className="space-y-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                onClick={() => handleAddZoomEffect(150)}
                disabled={selectedClips.length === 0}
              >
                Add 150% Zoom
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                onClick={() => handleAddZoomEffect(200)}
                disabled={selectedClips.length === 0}
              >
                Add 200% Zoom
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                onClick={() => handleAddZoomEffect(300)}
                disabled={selectedClips.length === 0}
              >
                Add 300% Zoom
              </Button>
            </div>
            
            {selectedClips.length > 0 && (
              <div className="mt-4">
                <p className="text-xs text-gray-400">
                  {selectedClips.length} clip{selectedClips.length > 1 ? 's' : ''} selected
                </p>
              </div>
            )}
          </div>

          {/* Canvas Size Selection */}
          <div className="p-4 border-b border-gray-700">
            <h3 className="font-semibold text-gray-200 mb-4">Canvas Size</h3>
            <div className="space-y-2">
              <Label className="text-gray-300 text-sm">Preview & Export Aspect Ratio</Label>
              <Select
                value={canvasSize}
                onValueChange={(value: '16:9' | '9:16' | '1:1' | '4:3') => setCanvasSize(value)}
              >
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="16:9">16:9 Landscape</SelectItem>
                  <SelectItem value="9:16">9:16 Portrait</SelectItem>
                  <SelectItem value="1:1">1:1 Square</SelectItem>
                  <SelectItem value="4:3">4:3 Traditional</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-400">
                {canvasSize === '16:9' && 'Landscape - YouTube, TV'}
                {canvasSize === '9:16' && 'Portrait - TikTok, Instagram Stories'}
                {canvasSize === '1:1' && 'Square - Instagram Posts'}
                {canvasSize === '4:3' && 'Traditional - Older TV format'}
              </p>
            </div>
          </div>

          <div className="p-4 border-b border-gray-700">
            <h3 className="font-semibold text-gray-200 mb-4">Project Files</h3>
            <div className="space-y-2">
              {videoClips.map(clip => (
                <Card
                  key={clip.id}
                  className={`p-3 cursor-pointer transition-colors ${
                    selectedClips.includes(clip.id)
                      ? 'bg-blue-600 border-blue-500' 
                      : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                  }`}
                  onClick={(e) => handleClipSelectWithHistory(clip.id, e.ctrlKey || e.metaKey)}
                >
                  <div className="text-sm font-medium text-white truncate">{clip.name}</div>
                  <div className="text-xs text-gray-400">
                    {formatTime(clip.endTime - clip.startTime)} ({formatTime(clip.startTime)} - {formatTime(clip.endTime)})
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {isUploading && (
            <div className="p-4 border-b border-gray-700">
              <h3 className="font-semibold text-gray-200 mb-2">Uploading...</h3>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}
        </div>
      </div>

      {/* Timeline */}
      <div className="h-1/3">
        <Timeline
            videoClips={videoClips}
            zoomEffects={zoomEffects}
            currentTime={currentTime}
            selectedClips={selectedClips}
            onTimeSeek={handleTimelineSeek}
            onClipSelect={handleClipSelectWithHistory}
            onClipTrim={handleClipTrimWithHistory}
            onClipMove={handleClipMoveWithHistory}
            onClipMute={handleClipMute}
            onZoomEffectMove={handleZoomEffectMove}
            onZoomEffectDelete={handleZoomEffectDelete}
            onZoomEffectTrim={handleZoomEffectTrim}
            zoom={timelineZoom}
            onZoomChange={handleTimelineZoomChange}
            subtitles={subtitles}
            selectedSubtitles={selectedSubtitles}
            onSubtitleSelect={handleSubtitleSelect}
            onSubtitleMove={handleSubtitleMove}
            onSubtitleTrim={handleSubtitleTrim}
        />
      </div>

      {/* Export Dialog */}
      <ExportDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        onExport={handleExportVideo}
        isExporting={isExporting}
        exportProgress={exportProgress}
        onCanvasSizeChange={setCanvasSize}
      />

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="video/*"
        onChange={(e) => handleFileUpload(e.target.files)}
        className="hidden"
      />
    </div>
  );
};
