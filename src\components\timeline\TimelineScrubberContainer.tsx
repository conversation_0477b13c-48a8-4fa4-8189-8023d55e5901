
import React from 'react';
import { TimelineRuler } from './TimelineRuler';
import { ThumbnailTrack } from './ThumbnailTrack';
import { PlayheadIndicator } from './PlayheadIndicator';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
}

interface TimelineScrubberContainerProps {
  videoClips: VideoClip[];
  currentTime: number;
  totalDuration: number;
  onTimeSeek: (time: number) => void;
  zoom: number;
}

export const TimelineScrubberContainer: React.FC<TimelineScrubberContainerProps> = ({
  videoClips,
  currentTime,
  totalDuration,
  onTimeSeek,
  zoom
}) => {
  const timelineWidth = Math.max((totalDuration * zoom) / 100 * 20, 800);

  return (
    <div className="w-full space-y-4 bg-gray-800">
      {/* Time Ruler */}
      <TimelineRuler
        totalDuration={totalDuration}
        timelineWidth={timelineWidth}
        currentTime={currentTime}
        onTimeSeek={onTimeSeek}
      />
      
      {/* Video Thumbnail Scrubber */}
      <div className="relative" style={{ minWidth: timelineWidth }}>
        <ThumbnailTrack
          videoClips={videoClips}
          totalDuration={totalDuration}
          timelineWidth={timelineWidth}
          onTimeSeek={onTimeSeek}
        />
        
        {/* Playhead Indicator */}
        <PlayheadIndicator
          currentTime={currentTime}
          totalDuration={totalDuration}
          timelineWidth={timelineWidth}
          onTimeSeek={onTimeSeek}
        />
      </div>
    </div>
  );
};
