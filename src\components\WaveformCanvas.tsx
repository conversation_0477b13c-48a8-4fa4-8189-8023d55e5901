
import React, { useRef, useEffect, useState } from 'react';

interface WaveformCanvasProps {
  audioUrl: string;
  width: number;
  height: number;
  className?: string;
  color?: string;
}

export const WaveformCanvas: React.FC<WaveformCanvasProps> = ({
  audioUrl,
  width,
  height,
  className = '',
  color = '#10b981'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [waveformData, setWaveformData] = useState<Float32Array | null>(null);

  useEffect(() => {
    const generateWaveform = async () => {
      try {
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        const response = await fetch(audioUrl);
        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        
        // Get audio data from first channel
        const channelData = audioBuffer.getChannelData(0);
        const samples = width; // One sample per pixel
        const blockSize = Math.floor(channelData.length / samples);
        const filteredData = new Float32Array(samples);
        
        // Downsample the audio data
        for (let i = 0; i < samples; i++) {
          const start = i * blockSize;
          const end = start + blockSize;
          let sum = 0;
          
          for (let j = start; j < end && j < channelData.length; j++) {
            sum += Math.abs(channelData[j]);
          }
          
          filteredData[i] = sum / blockSize;
        }
        
        setWaveformData(filteredData);
        audioContext.close();
      } catch (error) {
        console.error('Error generating waveform:', error);
      }
    };

    generateWaveform();
  }, [audioUrl, width]);

  useEffect(() => {
    if (!waveformData || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Set canvas size
    canvas.width = width;
    canvas.height = height;
    
    // Draw waveform
    ctx.fillStyle = color;
    const barWidth = width / waveformData.length;
    
    for (let i = 0; i < waveformData.length; i++) {
      const barHeight = (waveformData[i] * height) / 2;
      const x = i * barWidth;
      const y = (height - barHeight) / 2;
      
      ctx.fillRect(x, y, barWidth - 1, barHeight);
    }
  }, [waveformData, width, height, color]);

  return (
    <canvas
      ref={canvasRef}
      width={width}
      height={height}
      className={className}
    />
  );
};
