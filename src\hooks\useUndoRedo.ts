
import { useState, useCallback, useRef } from 'react';

interface UndoRedoState<T> {
  present: T;
  past: T[];
  future: T[];
}

interface UndoRedoActions {
  undo: () => void;
  redo: () => void;
  executeAction: (newState: any) => void;
  canUndo: boolean;
  canRedo: boolean;
  clearHistory: () => void;
}

export const useUndoRedo = <T>(initialState: T, maxHistorySize = 50): [T, UndoRedoActions] => {
  const [state, setState] = useState<UndoRedoState<T>>({
    present: initialState,
    past: [],
    future: []
  });

  const isExecutingRef = useRef(false);

  const undo = useCallback(() => {
    if (state.past.length === 0) return;

    const previous = state.past[state.past.length - 1];
    const newPast = state.past.slice(0, state.past.length - 1);

    isExecutingRef.current = true;
    setState({
      present: previous,
      past: newPast,
      future: [state.present, ...state.future]
    });
    isExecutingRef.current = false;
  }, [state]);

  const redo = useCallback(() => {
    if (state.future.length === 0) return;

    const next = state.future[0];
    const newFuture = state.future.slice(1);

    isExecutingRef.current = true;
    setState({
      present: next,
      past: [...state.past, state.present],
      future: newFuture
    });
    isExecutingRef.current = false;
  }, [state]);

  const executeAction = useCallback((newState: T) => {
    if (isExecutingRef.current) return;

    setState(prevState => {
      const newPast = [...prevState.past, prevState.present];
      
      // Limit history size
      if (newPast.length > maxHistorySize) {
        newPast.shift();
      }

      return {
        present: newState,
        past: newPast,
        future: []
      };
    });
  }, [maxHistorySize]);

  const clearHistory = useCallback(() => {
    setState(prevState => ({
      present: prevState.present,
      past: [],
      future: []
    }));
  }, []);

  const actions: UndoRedoActions = {
    undo,
    redo,
    executeAction,
    canUndo: state.past.length > 0,
    canRedo: state.future.length > 0,
    clearHistory
  };

  return [state.present, actions];
};
