import { ExportOptions } from '@/components/ExportDialog';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
  sourceStartTime?: number;
  sourceEndTime?: number;
}

interface ZoomEffect {
  id: string;
  startTime: number;
  endTime: number;
  zoomLevel: number;
  clipId: string;
}

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  color: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
}

export class VideoExportManager {
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private videoElements: Map<string, HTMLVideoElement> = new Map();
  private audioContext: AudioContext | null = null;
  private animationId: number | null = null;

  async exportVideo(
    videoClips: VideoClip[],
    zoomEffects: ZoomEffect[],
    subtitles: Subtitle[],
    options: ExportOptions,
    onProgress: (progress: number) => void
  ): Promise<Blob> {
    console.log('=== STARTING VIDEO EXPORT ===');
    
    // Calculate proper timeline bounds
    const timelineStart = Math.min(...videoClips.map(clip => clip.startTime));
    const timelineEnd = Math.max(...videoClips.map(clip => clip.endTime));
    const timelineDuration = timelineEnd - timelineStart;
    
    console.log('Timeline:', { timelineStart, timelineEnd, timelineDuration });

    // Setup canvas
    const { width, height } = this.getCanvasDimensions(options.resolution);
    this.canvas = document.createElement('canvas');
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext('2d');

    if (!this.ctx) {
      throw new Error('Failed to get canvas context');
    }

    // Initialize video elements
    await this.initializeVideoElements(videoClips);

    // Setup audio
    const audioStream = await this.setupAudio(videoClips);

    // Start recording
    return new Promise((resolve, reject) => {
      this.startRecording(audioStream, options, resolve, reject);
      this.renderVideoSequence(videoClips, zoomEffects, subtitles, timelineStart, timelineDuration, options.framerate, onProgress);
    });
  }

  private async initializeVideoElements(videoClips: VideoClip[]) {
    console.log('Initializing video elements...');
    
    for (const clip of videoClips) {
      const video = document.createElement('video');
      video.src = clip.url;
      video.muted = false; // Allow audio
      video.crossOrigin = 'anonymous';
      video.preload = 'metadata'; // Back to metadata for stability
      video.playsInline = true;

      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error(`Video setup timeout: ${clip.name}`));
        }, 10000);

        video.onloadedmetadata = () => {
          clearTimeout(timeout);
          console.log(`Video loaded: ${clip.name}, duration: ${video.duration}`);
          resolve();
        };

        video.onerror = (e) => {
          clearTimeout(timeout);
          console.error(`Video error for ${clip.name}:`, e);
          reject(new Error(`Video setup failed: ${clip.name}`));
        };
      });

      this.videoElements.set(clip.id, video);
    }
  }

  private async setupAudio(videoClips: VideoClip[]): Promise<MediaStream | null> {
    try {
      console.log('Setting up audio...');

      // Simple, stable audio context setup
      const audioContext = new AudioContext({ sampleRate: 44100 });
      const destination = audioContext.createMediaStreamDestination();

      // Simple master gain without complex processing
      const masterGain = audioContext.createGain();
      masterGain.gain.value = 1.0;
      masterGain.connect(destination);

      // Store audio context for cleanup
      this.audioContext = audioContext;

      // Connect video audio sources with minimal processing
      for (const clip of videoClips) {
        const video = this.videoElements.get(clip.id);
        if (video) {
          try {
            const source = audioContext.createMediaElementSource(video);
            const gainNode = audioContext.createGain();

            // Start muted, will be controlled during playback
            gainNode.gain.value = 0;

            source.connect(gainNode);
            gainNode.connect(masterGain);

            // Store gain node for later control
            (video as any).gainNode = gainNode;
            (video as any).audioContext = audioContext;
          } catch (error) {
            console.warn(`Audio setup failed for ${clip.name}:`, error);
          }
        }
      }

      return destination.stream;
    } catch (error) {
      console.error('Audio setup failed:', error);
      return null;
    }
  }

  private async renderVideoSequence(
    videoClips: VideoClip[],
    zoomEffects: ZoomEffect[],
    subtitles: Subtitle[],
    timelineStart: number,
    timelineDuration: number,
    framerate: number,
    onProgress: (progress: number) => void
  ) {
    console.log('Starting video sequence rendering...');

    // Cap framerate for stability
    const actualFramerate = Math.min(framerate, 24); // Max 24fps for stability
    const frameInterval = 1000 / actualFramerate;
    const totalFrames = Math.ceil(timelineDuration * actualFramerate);
    let currentFrame = 0;

    const renderNextFrame = async () => {
      if (currentFrame >= totalFrames) {
        console.log('Rendering complete, stopping recorder...');
        // Simple stop without complex audio fading
        setTimeout(() => this.mediaRecorder?.stop(), 200);
        return;
      }

      const currentTime = timelineStart + (currentFrame / actualFramerate);
      await this.renderFrame(currentTime, videoClips, zoomEffects, subtitles);

      currentFrame++;
      const progress = (currentFrame / totalFrames) * 100;
      onProgress(Math.min(progress, 100));

      // Longer delay for more stable processing
      setTimeout(renderNextFrame, Math.max(frameInterval, 50)); // At least 50ms between frames
    };

    renderNextFrame();
  }

  private async renderFrame(currentTime: number, videoClips: VideoClip[], zoomEffects: ZoomEffect[], subtitles: Subtitle[]) {
    if (!this.ctx || !this.canvas) return;

    // Clear canvas
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // Find active clip
    const activeClip = videoClips.find(clip =>
      currentTime >= clip.startTime && currentTime < clip.endTime
    );

    if (!activeClip) {
      // Mute all audio when no clip is active
      this.videoElements.forEach(video => {
        if ((video as any).gainNode) {
          (video as any).gainNode.gain.value = 0;
        }
      });
      return;
    }

    const video = this.videoElements.get(activeClip.id);
    if (!video) return;

    // Calculate source time
    const clipProgress = (currentTime - activeClip.startTime) / (activeClip.endTime - activeClip.startTime);
    const sourceStartTime = activeClip.sourceStartTime || 0;
    const sourceEndTime = activeClip.sourceEndTime || video.duration;
    const sourceDuration = sourceEndTime - sourceStartTime;
    const targetSourceTime = sourceStartTime + (clipProgress * sourceDuration);

    // Very conservative video time handling to minimize glitches
    const timeDiff = Math.abs(video.currentTime - targetSourceTime);
    if (timeDiff > 0.5) { // Very large threshold to minimize seeking
      video.currentTime = targetSourceTime;
      // Give video more time to stabilize after seeking
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    // Ensure video is playing for audio - but don't force it every frame
    if (video.paused && timeDiff < 0.5) { // Only play if we're close to target time
      try {
        await video.play();
      } catch (error) {
        // Ignore play errors silently
      }
    }

    // Simple, immediate audio control - no scheduled changes
    this.videoElements.forEach((v, clipId) => {
      if ((v as any).gainNode) {
        const gainNode = (v as any).gainNode;
        // Use immediate value changes to avoid audio glitches
        gainNode.gain.value = clipId === activeClip.id ? 0.8 : 0;
      }
    });

    // Apply zoom
    const zoomLevel = this.getCurrentZoomLevel(currentTime, activeClip.id, zoomEffects);
    const scale = zoomLevel / 100;
    
    this.ctx.save();
    
    if (scale !== 1) {
      const offsetX = (this.canvas.width * (1 - scale)) / 2;
      const offsetY = (this.canvas.height * (1 - scale)) / 2;
      this.ctx.translate(offsetX, offsetY);
      this.ctx.scale(scale, scale);
    }

    // Draw video frame with proper aspect ratio
    try {
      // Get video dimensions
      const videoWidth = video.videoWidth;
      const videoHeight = video.videoHeight;
      const canvasWidth = this.canvas.width;
      const canvasHeight = this.canvas.height;
      
      if (videoWidth && videoHeight) {
        // Calculate aspect ratios
        const videoAspectRatio = videoWidth / videoHeight;
        const canvasAspectRatio = canvasWidth / canvasHeight;
        
        let drawWidth, drawHeight, drawX, drawY;
        
        if (videoAspectRatio > canvasAspectRatio) {
          // Video is wider than canvas - fit to width, add letterboxing
          drawWidth = canvasWidth;
          drawHeight = canvasWidth / videoAspectRatio;
          drawX = 0;
          drawY = (canvasHeight - drawHeight) / 2;
        } else {
          // Video is taller than canvas - fit to height, add pillarboxing
          drawWidth = canvasHeight * videoAspectRatio;
          drawHeight = canvasHeight;
          drawX = (canvasWidth - drawWidth) / 2;
          drawY = 0;
        }
        
        this.ctx.drawImage(video, drawX, drawY, drawWidth, drawHeight);
      } else {
        // Fallback to original behavior if video dimensions are not available
        this.ctx.drawImage(video, 0, 0, canvasWidth, canvasHeight);
      }
    } catch (error) {
      console.warn('Frame draw error:', error);
    }
    
    this.ctx.restore();
    
    // Render subtitles
    const activeSubtitle = subtitles.find(subtitle => 
      currentTime >= subtitle.startTime && currentTime < subtitle.endTime
    );
    
    // Debug logging for subtitle timing
    if (subtitles.length > 0) {
      console.log(`Export time: ${currentTime.toFixed(2)}s, Subtitles:`, 
        subtitles.map(s => `${s.text} (${s.startTime}-${s.endTime})`),
        `Active: ${activeSubtitle ? activeSubtitle.text : 'none'}`);
    }
    
    if (activeSubtitle) {
      this.renderSubtitle(activeSubtitle);
    }
  }

  private getCurrentZoomLevel(currentTime: number, clipId: string, zoomEffects: ZoomEffect[]): number {
    const activeZoomEffect = zoomEffects.find(effect => 
      effect.clipId === clipId &&
      currentTime >= effect.startTime && 
      currentTime <= effect.endTime
    );
    return activeZoomEffect ? activeZoomEffect.zoomLevel : 100;
  }

  private renderSubtitle(subtitle: Subtitle) {
    if (!this.ctx || !this.canvas) return;

    this.ctx.save();

    // Scale font size based on canvas resolution
    // Reference size is 720p (1280x720), scale font accordingly
    const referenceHeight = 720;
    const scaleFactor = this.canvas.height / referenceHeight;
    const scaledFontSize = Math.round(subtitle.fontSize * scaleFactor);

    // Set font
    this.ctx.font = `${scaledFontSize}px ${subtitle.fontFamily}`;
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';

    // Calculate text position
    const centerX = this.canvas.width / 2;
    let y: number;

    switch (subtitle.position) {
      case 'top':
        y = scaledFontSize + Math.round(20 * scaleFactor);
        break;
      case 'center':
        y = this.canvas.height / 2;
        break;
      case 'bottom':
      default:
        y = this.canvas.height - scaledFontSize - Math.round(20 * scaleFactor);
        break;
    }
    
    // Measure text for background
    const textMetrics = this.ctx.measureText(subtitle.text);
    const textWidth = textMetrics.width;
    const textHeight = scaledFontSize;

    // Draw background if specified
    if (subtitle.backgroundColor && subtitle.backgroundColor !== 'transparent') {
      this.ctx.fillStyle = subtitle.backgroundColor;
      const padding = Math.round(10 * scaleFactor);
      this.ctx.fillRect(
        centerX - textWidth / 2 - padding,
        y - textHeight / 2 - padding,
        textWidth + padding * 2,
        textHeight + padding * 2
      );
    }
    
    // Draw text
    this.ctx.fillStyle = subtitle.fontColor;
    this.ctx.fillText(subtitle.text, centerX, y);
    
    this.ctx.restore();
  }

  private startRecording(
    audioStream: MediaStream | null,
    options: ExportOptions,
    resolve: (blob: Blob) => void,
    reject: (error: Error) => void
  ) {
    if (!this.canvas) {
      reject(new Error('Canvas not initialized'));
      return;
    }

    console.log('Starting MediaRecorder...');

    // Use a more conservative frame rate for canvas capture to reduce glitches
    const captureFrameRate = Math.min(options.framerate, 30); // Cap at 30fps for stability
    const videoStream = this.canvas.captureStream(captureFrameRate);
    let combinedStream = videoStream;

    // Combine video and audio streams
    if (audioStream && audioStream.getAudioTracks().length > 0) {
      console.log('Adding audio tracks to stream');
      combinedStream = new MediaStream([
        ...videoStream.getVideoTracks(),
        ...audioStream.getAudioTracks()
      ]);
    }

    const mimeType = this.getMimeType(options.format);
    const videoBitsPerSecond = this.getBitrate(options.quality, options.resolution);

    // Simple, stable MediaRecorder settings
    this.mediaRecorder = new MediaRecorder(combinedStream, {
      mimeType,
      videoBitsPerSecond,
      audioBitsPerSecond: 128000, // Standard audio bitrate
    });

    this.recordedChunks = [];

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.recordedChunks.push(event.data);
      }
    };

    this.mediaRecorder.onstop = () => {
      console.log('MediaRecorder stopped, creating blob...');
      const blob = new Blob(this.recordedChunks, { type: mimeType });
      console.log(`Export complete! Blob size: ${blob.size} bytes`);
      this.cleanup();
      resolve(blob);
    };

    this.mediaRecorder.onerror = (event) => {
      console.error('MediaRecorder error:', event);
      this.cleanup();
      reject(new Error('Recording failed'));
    };

    // Standard timing for stability
    this.mediaRecorder.start(100); // Collect data every 100ms
    console.log('MediaRecorder started');
  }

  private calculateTimelineBounds(videoClips: VideoClip[]): { timelineDuration: number; timelineStart: number } {
    if (videoClips.length === 0) return { timelineDuration: 0, timelineStart: 0 };
    
    const timelineStart = Math.min(...videoClips.map(clip => clip.startTime));
    const timelineEnd = Math.max(...videoClips.map(clip => clip.endTime));
    const timelineDuration = timelineEnd - timelineStart;
    
    return { timelineDuration, timelineStart };
  }

  private getCanvasDimensions(resolution: string): { width: number; height: number } {
    switch (resolution) {
      case '720p': return { width: 1280, height: 720 };
      case '1080p': return { width: 1920, height: 1080 };
      case '1440p': return { width: 2560, height: 1440 };
      case '4k': return { width: 3840, height: 2160 };
      default: return { width: 1280, height: 720 };
    }
  }

  private getMimeType(format: string): string {
    switch (format) {
      case 'mp4': return 'video/mp4';
      case 'webm': return 'video/webm';
      case 'mov': return 'video/mp4';
      default: return 'video/mp4';
    }
  }

  private getBitrate(quality: string, resolution: string): number {
    const baseRates = {
      '720p': { low: 1000000, medium: 2500000, high: 5000000, ultra: 8000000 },
      '1080p': { low: 2000000, medium: 5000000, high: 8000000, ultra: 12000000 },
      '1440p': { low: 4000000, medium: 8000000, high: 12000000, ultra: 18000000 },
      '4k': { low: 8000000, medium: 15000000, high: 25000000, ultra: 40000000 },
    };

    return baseRates[resolution as keyof typeof baseRates][quality as keyof typeof baseRates['720p']];
  }

  downloadBlob(blob: Blob, filename: string) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  private cleanup() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    this.videoElements.forEach(video => {
      video.pause();
      video.src = '';
      video.load();
    });
    this.videoElements.clear();

    // Clean up audio context
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
    }
    this.audioContext = null;

    this.canvas = null;
    this.ctx = null;
  }


}
