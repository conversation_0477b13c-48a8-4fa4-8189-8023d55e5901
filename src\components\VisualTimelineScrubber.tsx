
import React from 'react';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
}

interface VisualTimelineScrubberProps {
  videoClips: VideoClip[];
  currentTime: number;
  totalDuration: number;
  onTimeSeek: (time: number) => void;
  zoom: number;
}

// This component is deprecated - functionality has been integrated into the main Timeline
export const VisualTimelineScrubber: React.FC<VisualTimelineScrubberProps> = () => {
  return null;
};
