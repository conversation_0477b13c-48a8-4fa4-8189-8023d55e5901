
import React from 'react';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
}

interface ThumbnailTrackProps {
  videoClips: VideoClip[];
  totalDuration: number;
  timelineWidth: number;
  onTimeSeek: (time: number) => void;
}

export const ThumbnailTrack: React.FC<ThumbnailTrackProps> = ({
  videoClips,
  totalDuration,
  timelineWidth,
  onTimeSeek
}) => {
  const handleClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickTime = (clickX / timelineWidth) * totalDuration;
    onTimeSeek(Math.max(0, Math.min(totalDuration, clickTime)));
  };

  return (
    <div 
      className="relative h-20 bg-gray-800 rounded-lg overflow-hidden cursor-pointer border border-gray-600"
      style={{ width: timelineWidth }}
      onClick={handleClick}
    >
      {/* Clean thumbnail track without audio waveform overlay */}
      <div className="absolute inset-0 bg-gray-800">
        {videoClips.map((clip) => (
          <div
            key={clip.id}
            className="absolute h-full rounded overflow-hidden bg-gray-800"
            style={{
              left: (clip.startTime / totalDuration) * timelineWidth,
              width: ((clip.endTime - clip.startTime) / totalDuration) * timelineWidth,
            }}
          >
            <div className="w-full h-full bg-gradient-to-r from-editor-teal/80 to-editor-teal/60 flex items-center justify-center">
              <video
                src={clip.url}
                className="w-full h-full object-cover opacity-75"
                muted
                preload="metadata"
              />
              <div className="absolute inset-0 bg-black/20" />
              <span className="absolute bottom-1 left-2 text-xs text-white font-medium truncate max-w-[80%] bg-gray-800/50 px-1 rounded">
                {clip.name}
              </span>
            </div>
          </div>
        ))}
      </div>
      
      {/* Time markers on scrubber */}
      <div className="absolute top-0 left-0 right-0 h-2 bg-gray-800 pointer-events-none">
        {Array.from({ length: Math.floor(totalDuration / 10) + 1 }, (_, i) => (
          <div
            key={i}
            className="absolute top-0 w-px h-2 bg-white/30"
            style={{ left: (i * 10 / totalDuration) * timelineWidth }}
          />
        ))}
      </div>
    </div>
  );
};
