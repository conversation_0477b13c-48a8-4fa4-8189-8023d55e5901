
import React, { useRef, useEffect } from 'react';

interface VideoOverlayProps {
  width: number;
  height: number;
  zoom: number;
  showGuides?: boolean;
  showRulers?: boolean;
}

export const VideoOverlay: React.FC<VideoOverlayProps> = ({
  width,
  height,
  zoom,
  showGuides = true,
  showRulers = true
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = width;
    canvas.height = height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    if (showGuides) {
      // Draw center guides
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.lineWidth = 1;
      ctx.setLineDash([5, 5]);

      // Vertical center line
      ctx.beginPath();
      ctx.moveTo(width / 2, 0);
      ctx.lineTo(width / 2, height);
      ctx.stroke();

      // Horizontal center line
      ctx.beginPath();
      ctx.moveTo(0, height / 2);
      ctx.lineTo(width, height / 2);
      ctx.stroke();

      // Rule of thirds grid
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.15)';
      ctx.setLineDash([3, 3]);

      // Vertical thirds
      ctx.beginPath();
      ctx.moveTo(width / 3, 0);
      ctx.lineTo(width / 3, height);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo((width * 2) / 3, 0);
      ctx.lineTo((width * 2) / 3, height);
      ctx.stroke();

      // Horizontal thirds
      ctx.beginPath();
      ctx.moveTo(0, height / 3);
      ctx.lineTo(width, height / 3);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(0, (height * 2) / 3);
      ctx.lineTo(width, (height * 2) / 3);
      ctx.stroke();
    }

    if (showRulers && zoom > 100) {
      // Draw rulers when zoomed in
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(0, 0, width, 20); // Top ruler
      ctx.fillRect(0, 0, 20, height); // Left ruler

      ctx.fillStyle = 'white';
      ctx.font = '10px monospace';
      ctx.textAlign = 'center';

      // Top ruler markings
      for (let i = 0; i < width; i += 50) {
        ctx.fillText(i.toString(), i, 15);
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 1;
        ctx.setLineDash([]);
        ctx.beginPath();
        ctx.moveTo(i, 20);
        ctx.lineTo(i, 25);
        ctx.stroke();
      }

      // Left ruler markings
      ctx.save();
      ctx.translate(10, 0);
      ctx.rotate(-Math.PI / 2);
      for (let i = 0; i < height; i += 50) {
        ctx.fillText(i.toString(), -i, 5);
      }
      ctx.restore();
    }
  }, [width, height, zoom, showGuides, showRulers]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none z-10"
      style={{
        width: '100%',
        height: '100%'
      }}
    />
  );
};
