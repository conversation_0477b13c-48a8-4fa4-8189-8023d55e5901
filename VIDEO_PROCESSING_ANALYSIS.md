# Video Processing Implementation Analysis

## Overview

The video editor implements a sophisticated dual-layer architecture with real-time preview and professional export capabilities. Here's how both systems work:

## 1. PREVIEW SYSTEM (Real-time Playback)

### Architecture
The preview system uses native HTML5 video elements for real-time playback with timeline synchronization.

### Key Components

#### A. VideoPreview Component (`src/components/VideoPreview.tsx`)
- **Purpose**: Displays the current video frame with zoom/pan capabilities
- **Core Element**: Single `<video>` element that switches sources based on timeline position
- **Features**:
  - Real-time zoom (100% to 500%) with smooth transitions
  - Pan functionality when zoomed > 100%
  - Keyboard shortcuts (Space, Arrow keys, F for fullscreen)
  - Frame-accurate navigation (30fps assumed)
  - Subtitle overlay rendering
  - Video quality indicators

#### B. Timeline Synchronization (`src/components/VideoEditor.tsx`)
```typescript
// Core function that determines which clip should play
const getCurrentPlayingClip = useCallback(() => {
  return videoClips.find(clip => 
    currentTime >= clip.startTime && currentTime < clip.endTime
  );
}, [videoClips, currentTime]);
```

#### C. Time Management System
- **Master Timeline**: Global timeline that spans all clips
- **Clip Mapping**: Maps timeline time to source video time
- **Seeking Logic**: 
  ```typescript
  const clipProgress = (currentTime - clip.startTime) / (clip.endTime - clip.startTime);
  const targetSourceTime = sourceStartTime + (clipProgress * sourceDuration);
  ```

### Preview Workflow
1. **Timeline Position Change** → `handleSeek()` called
2. **Find Target Clip** → `getCurrentPlayingClip()` determines active clip
3. **Calculate Source Time** → Maps timeline time to video source time
4. **Update Video Element** → Sets `video.currentTime` and `video.src`
5. **Apply Effects** → Zoom effects applied via CSS transforms
6. **Render Subtitles** → Overlay component renders active subtitles

### Performance Optimizations
- **Preloading**: Videos preload metadata for faster switching
- **Smooth Zoom**: Uses `requestAnimationFrame` for smooth zoom transitions
- **Efficient Seeking**: Only seeks when time difference > threshold
- **Event Throttling**: Limits time update frequency

## 2. EXPORT SYSTEM (MediaBunny Implementation)

### Architecture
The export system uses MediaBunny for professional-grade video processing with frame-by-frame rendering.

### Key Components

#### A. MediaBunnyExporter (`src/utils/mediaBunnyExporter.ts`)
- **Purpose**: Handles complete video export pipeline
- **Technology**: MediaBunny library for video/audio processing
- **Output**: Professional video files (MP4, WebM, MOV)

#### B. Export Pipeline

##### Phase 1: Initialization
```typescript
// 1. Setup canvas for frame rendering
this.canvas = document.createElement('canvas');
this.canvas.width = width; // Based on resolution (720p, 1080p, etc.)
this.canvas.height = height;

// 2. Initialize video elements for each clip
await this.initializeVideoElements(videoClips);

// 3. Setup MediaBunny audio processing
await this.initializeAudioInputs(videoClips);

// 4. Create AudioContext with proper sample rate
this.audioContext = new AudioContext({ sampleRate: audioSampleRate });
```

##### Phase 2: MediaBunny Output Setup
```typescript
// Create MediaBunny output pipeline
const output = new Output({
  format: this.getOutputFormat(options.format), // MP4/WebM/MOV
  target: new BufferTarget(), // Output to memory buffer
});

// Add video track
const videoSource = new CanvasSource(this.canvas, {
  codec: this.getVideoCodec(options.format), // H.264/VP9
  bitrate: this.getBitrate(options.quality, options.resolution),
});
output.addVideoTrack(videoSource);

// Add audio track
const audioSource = new AudioBufferSource({
  codec: this.getAudioCodec(options.format), // AAC/Opus
  bitrate: this.getAudioBitrate(options.quality),
  sampleRate: sampleRate,
  numberOfChannels: 2
});
output.addAudioTrack(audioSource);
```

##### Phase 3: Frame-by-Frame Rendering
```typescript
const frameInterval = 1 / framerate; // e.g., 1/30 = 0.033s per frame
const totalFrames = Math.ceil(timelineDuration * framerate);

for (let frame = 0; frame < totalFrames; frame++) {
  const currentTime = timelineStart + (frame * frameInterval);
  
  // 1. Render frame to canvas
  await this.renderFrame(currentTime, videoClips, zoomEffects, subtitles);
  
  // 2. Add canvas frame to MediaBunny video source
  await videoSource.add(currentTime, frameInterval);
  
  // 3. Process audio for this time slice
  if (audioSource) {
    await this.processAudioForTimeSlice(currentTime, frameInterval, audioSource, videoClips);
  }
}
```

#### C. Frame Rendering Process (`renderFrame()`)
1. **Clear Canvas**: Fill with black background
2. **Find Active Clip**: Determine which clip should be visible
3. **Calculate Source Time**: Map timeline time to clip source time
4. **Seek Video Element**: Set video to correct frame
5. **Wait for Frame**: Ensure video is ready (`readyState >= 2`)
6. **Apply Zoom Effects**: Calculate zoom level and apply transforms
7. **Draw Video Frame**: Render video to canvas with proper scaling
8. **Render Subtitles**: Draw active subtitles on top

#### D. Audio Processing System

##### Audio Initialization
```typescript
// For each video clip with audio:
const input = new Input({
  formats: ALL_FORMATS,
  source: new BlobSource(clip.file)
});

const audioTrack = await input.getPrimaryAudioTrack();
const audioSink = new AudioBufferSink(audioTrack); // Key: AudioBufferSink, not AudioSampleSink
```

##### Audio Processing During Export
```typescript
// For each frame time slice:
const audioIterator = audioSink.buffers(targetSourceTime, targetSourceTime + duration);
const audioResult = await audioIterator.next();

if (audioResult.value) {
  const { buffer } = audioResult.value;
  const frameBuffer = this.createFrameAudioBuffer(buffer, duration, targetSourceTime);
  await audioSource.add(frameBuffer); // Add to MediaBunny audio track
}
```

### Export Quality Settings

#### Video Quality
- **Low**: 1-4 Mbps depending on resolution
- **Medium**: 2.5-8 Mbps depending on resolution  
- **High**: 5-25 Mbps depending on resolution
- **Ultra**: Maximum quality settings

#### Audio Quality
- **Low**: 96 kbps
- **Medium**: 128 kbps
- **High**: 192 kbps

#### Supported Formats
- **MP4**: H.264 video + AAC audio (universal compatibility)
- **WebM**: VP9 video + Opus audio (web optimized)
- **MOV**: H.264 video + AAC audio (high quality)

## 3. KEY DIFFERENCES: PREVIEW vs EXPORT

| Aspect | Preview System | Export System |
|--------|----------------|---------------|
| **Technology** | HTML5 Video | MediaBunny + Canvas |
| **Performance** | Real-time | Frame-by-frame |
| **Quality** | Display quality | Export quality |
| **Audio** | Native playback | Professional encoding |
| **Effects** | CSS transforms | Canvas rendering |
| **Seeking** | Video element seeking | Precise frame positioning |
| **Output** | Visual display | Video file |

## 4. SYNCHRONIZATION MECHANISMS

### Preview Synchronization
- Uses `video.currentTime` for seeking
- Timeline drives video element updates
- Effects applied via CSS transforms
- Audio handled by browser

### Export Synchronization  
- Uses AudioContext as master clock
- Frame-precise timing calculations
- Effects rendered to canvas
- Audio processed through MediaBunny pipeline

## 5. PERFORMANCE CONSIDERATIONS

### Preview Optimizations
- Minimal seeking (only when necessary)
- Preloaded video metadata
- Efficient DOM updates
- Throttled time updates

### Export Optimizations
- Canvas reuse for all frames
- Audio buffer management
- Progress reporting
- Memory cleanup after export

This dual-system approach provides smooth real-time preview while ensuring professional export quality.
