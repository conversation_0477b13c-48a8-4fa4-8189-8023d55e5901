import { MediaBunnyExporter } from './mediaBunnyExporter';
import { VideoExportManager } from './videoExportManager';
import { ExportOptions } from '@/components/ExportDialog';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
  sourceStartTime?: number;
  sourceEndTime?: number;
  muted?: boolean;
}

interface ZoomEffect {
  id: string;
  startTime: number;
  endTime: number;
  zoomLevel: number;
  clipId: string;
}

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
}

export class HybridVideoExporter {
  private mediaBunnyExporter: MediaBunnyExporter;
  private fallbackExporter: VideoExportManager;

  constructor() {
    this.mediaBunnyExporter = new MediaBunnyExporter();
    this.fallbackExporter = new VideoExportManager();
  }

  async exportVideo(
    videoClips: VideoClip[],
    zoomEffects: ZoomEffect[],
    subtitles: Subtitle[],
    options: ExportOptions,
    onProgress: (progress: number) => void
  ): Promise<Blob> {
    console.log('=== STARTING HYBRID EXPORT ===');
    
    // Try MediaBunny first for better quality
    try {
      console.log('Attempting MediaBunny export...');
      const blob = await this.mediaBunnyExporter.exportVideo(
        videoClips,
        zoomEffects,
        subtitles,
        options,
        onProgress
      );
      console.log('MediaBunny export successful!');
      return blob;
    } catch (error) {
      console.warn('MediaBunny export failed, falling back to original method:', error);
      
      // Reset progress for fallback
      onProgress(0);
      
      // Fallback to original export method
      try {
        console.log('Attempting fallback export...');
        const blob = await this.fallbackExporter.exportVideo(
          videoClips,
          zoomEffects,
          subtitles,
          options,
          onProgress
        );
        console.log('Fallback export successful!');
        return blob;
      } catch (fallbackError) {
        console.error('Both export methods failed:', fallbackError);
        throw new Error('Export failed: Both MediaBunny and fallback methods encountered errors');
      }
    }
  }

  downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}
