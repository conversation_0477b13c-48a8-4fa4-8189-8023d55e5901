
import React, { useRef, useState, useCallback } from 'react';

interface PlayheadIndicatorProps {
  currentTime: number;
  totalDuration: number;
  timelineWidth: number;
  onTimeSeek: (time: number) => void;
}

export const PlayheadIndicator: React.FC<PlayheadIndicatorProps> = ({
  currentTime,
  totalDuration,
  timelineWidth,
  onTimeSeek
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  const playheadPosition = (currentTime / totalDuration) * timelineWidth;

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    e.preventDefault();
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const time = (mouseX / timelineWidth) * totalDuration;
    
    onTimeSeek(Math.max(0, Math.min(totalDuration, time)));
  }, [isDragging, timelineWidth, totalDuration, onTimeSeek]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div ref={containerRef} className="absolute inset-0 pointer-events-none" style={{ width: timelineWidth }}>
      {/* Playhead */}
      <div
        className="absolute top-0 bottom-0 w-1 scrubber-handle z-20 pointer-events-auto cursor-pointer"
        style={{ left: playheadPosition }}
        onMouseDown={handleMouseDown}
      >
        <div className="absolute -top-2 -left-2 w-5 h-5 bg-editor-teal rounded-full shadow-lg" />
        <div className="w-full h-full bg-editor-teal shadow-lg" />
      </div>
      
      {/* Current time display */}
      <div
        className="absolute -top-8 transform -translate-x-1/2 bg-gray-800 text-white text-sm px-2 py-1 rounded pointer-events-none z-30 border border-gray-600"
        style={{ left: playheadPosition }}
      >
        {Math.floor(currentTime / 60)}:{(currentTime % 60).toFixed(1).padStart(4, '0')}
      </div>
    </div>
  );
};
