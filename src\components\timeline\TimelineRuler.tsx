
import React from 'react';

interface TimelineRulerProps {
  totalDuration: number;
  timelineWidth: number;
  currentTime: number;
  onTimeSeek: (time: number) => void;
  showGrid?: boolean;
}

export const TimelineRuler: React.FC<TimelineRulerProps> = ({
  totalDuration,
  timelineWidth,
  currentTime,
  onTimeSeek,
  showGrid = true
}) => {
  const handleClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickTime = (clickX / timelineWidth) * totalDuration;
    onTimeSeek(Math.max(0, Math.min(totalDuration, clickTime)));
  };

  const getTimeMarkers = () => {
    const markers = [];
    const interval = totalDuration <= 60 ? 5 : totalDuration <= 300 ? 15 : 30;
    
    for (let i = 0; i <= totalDuration; i += interval) {
      markers.push({
        time: i,
        position: (i / totalDuration) * timelineWidth,
        label: `${Math.floor(i / 60)}:${(i % 60).toString().padStart(2, '0')}`
      });
    }
    
    return markers;
  };

  // Validate totalDuration to prevent invalid array length
  const isValidDuration = !isNaN(totalDuration) && isFinite(totalDuration) && totalDuration > 0;
  const safeGridCount = isValidDuration ? Math.min(Math.ceil(totalDuration / 5) + 1, 1000) : 0;

  const timeMarkers = isValidDuration ? getTimeMarkers() : [];

  return (
    <div className="relative h-8 bg-gray-800" style={{ minWidth: timelineWidth }}>
      <div 
        className="absolute inset-0 bg-gray-800 border-b border-gray-600 cursor-pointer" 
        style={{ width: timelineWidth }}
        onClick={handleClick}
      >
        {/* Grid lines (if enabled and duration is valid) */}
        {showGrid && isValidDuration && safeGridCount > 0 && (
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: safeGridCount }, (_, i) => (
              <div
                key={`grid-${i}`}
                className="absolute top-0 bottom-0 w-px bg-gray-600 opacity-30"
                style={{ left: (i * 5 / totalDuration) * timelineWidth }}
              />
            ))}
          </div>
        )}

        {timeMarkers.map((marker, index) => (
          <div
            key={index}
            className="absolute top-0 h-full flex flex-col justify-between bg-gray-800"
            style={{ left: marker.position }}
          >
            <div className="w-px h-4 bg-gray-400" />
            <span className="text-xs text-gray-300 transform -translate-x-1/2 whitespace-nowrap bg-gray-800">
              {marker.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};
