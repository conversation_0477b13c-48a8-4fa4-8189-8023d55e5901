
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 180 83% 37%;
    --primary-foreground: 210 40% 98%;

    --secondary: 263 83% 57%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 180 83% 37%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 180 83% 37%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 180 83% 37%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 180 83% 37%;

    /* Custom video editor colors */
    --editor-bg: 220 13% 9%;
    --editor-panel: 220 13% 11%;
    --editor-surface: 220 13% 14%;
    --editor-border: 220 13% 20%;
    --editor-text: 210 40% 95%;
    --editor-text-muted: 215 20% 65%;
    --editor-accent-teal: 180 83% 47%;
    --editor-accent-purple: 263 83% 67%;
  }

  .dark {
    --background: 220 13% 9%;
    --foreground: 210 40% 98%;

    --card: 220 13% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 220 13% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 180 83% 47%;
    --primary-foreground: 220 13% 9%;

    --secondary: 263 83% 67%;
    --secondary-foreground: 220 13% 9%;

    --muted: 220 13% 14%;
    --muted-foreground: 215 20% 65%;

    --accent: 180 83% 47%;
    --accent-foreground: 220 13% 9%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 20%;
    --input: 220 13% 14%;
    --ring: 180 83% 47%;
    
    --sidebar-background: 220 13% 9%;
    --sidebar-foreground: 210 40% 95%;
    --sidebar-primary: 180 83% 47%;
    --sidebar-primary-foreground: 220 13% 9%;
    --sidebar-accent: 220 13% 14%;
    --sidebar-accent-foreground: 210 40% 95%;
    --sidebar-border: 220 13% 20%;
    --sidebar-ring: 180 83% 47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }
}

/* Custom animations for micro-interactions */
@layer utilities {
  .animate-pulse-subtle {
    animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .animate-bounce-gentle {
    animation: bounce-gentle 1s ease-in-out infinite;
  }
  
  .animate-scale-in {
    animation: scale-in 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .hover-lift {
    @apply transition-all duration-200 ease-out;
  }
  
  .hover-lift:hover {
    @apply transform -translate-y-1 shadow-lg;
  }
  
  .interactive-element {
    @apply transition-all duration-200 ease-out hover:scale-105 active:scale-95;
  }
  
  .glass-morphism {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Video editor specific styles */
.video-editor-bg {
  background: hsl(var(--editor-bg));
}

.video-editor-panel {
  background: hsl(var(--editor-panel));
}

.video-editor-surface {
  background: hsl(var(--editor-surface));
}

.timeline-track {
  background: linear-gradient(135deg, hsl(var(--editor-surface)) 0%, hsl(var(--editor-panel)) 100%);
  border: 1px solid hsl(var(--editor-border));
}

.clip-video {
  background: linear-gradient(135deg, hsl(var(--editor-accent-teal)) 0%, hsl(var(--editor-accent-teal))/80% 100%);
}

.clip-zoom {
  background: linear-gradient(135deg, hsl(var(--editor-accent-purple)) 0%, hsl(var(--editor-accent-purple))/80% 100%);
}

.scrubber-handle {
  background: hsl(var(--editor-accent-teal));
  box-shadow: 0 4px 12px hsl(var(--editor-accent-teal))/25%;
}

.upload-zone {
  background: linear-gradient(135deg, hsl(var(--editor-surface))/50% 0%, hsl(var(--editor-panel))/30% 100%);
  border: 2px dashed hsl(var(--editor-accent-teal))/30%;
  transition: all 0.3s ease;
}

.upload-zone:hover {
  border-color: hsl(var(--editor-accent-teal));
  background: linear-gradient(135deg, hsl(var(--editor-accent-teal))/10% 0%, hsl(var(--editor-accent-purple))/5% 100%);
}

.upload-zone.drag-over {
  border-color: hsl(var(--editor-accent-teal));
  background: linear-gradient(135deg, hsl(var(--editor-accent-teal))/20% 0%, hsl(var(--editor-accent-purple))/10% 100%);
  transform: scale(1.02);
}
