
import React, { useCallback } from 'react';
import { ZoomIn, ZoomOut, Maximize } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';

interface VideoZoomControlsProps {
  zoom: number;
  onZoomChange: (zoom: number) => void;
  onFitToScreen: () => void;
}

export const VideoZoomControls: React.FC<VideoZoomControlsProps> = ({
  zoom,
  onZoomChange,
  onFitToScreen
}) => {
  const handleZoomIn = useCallback(() => {
    onZoomChange(Math.min(400, zoom + 25));
  }, [zoom, onZoomChange]);

  const handleZoomOut = useCallback(() => {
    onZoomChange(Math.max(25, zoom - 25));
  }, [zoom, onZoomChange]);

  return (
    <div className="absolute top-4 right-4 bg-gray-800/90 backdrop-blur-sm rounded-lg p-3 flex items-center gap-2">
      <Button
        variant="ghost"
        size="sm"
        onClick={handleZoomOut}
        className="text-white hover:bg-gray-700 p-1 h-8 w-8"
      >
        <ZoomOut className="w-4 h-4" />
      </Button>
      
      <div className="flex items-center gap-2">
        <span className="text-xs text-gray-400">Zoom:</span>
        <Slider
          value={[zoom]}
          onValueChange={(value) => onZoomChange(value[0])}
          max={400}
          min={25}
          step={25}
          className="w-20"
        />
        <span className="text-xs text-gray-400 w-12">{zoom}%</span>
      </div>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={handleZoomIn}
        className="text-white hover:bg-gray-700 p-1 h-8 w-8"
      >
        <ZoomIn className="w-4 h-4" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={onFitToScreen}
        className="text-white hover:bg-gray-700 p-1 h-8 w-8"
        title="Fit to screen"
      >
        <Maximize className="w-4 h-4" />
      </Button>
    </div>
  );
};
