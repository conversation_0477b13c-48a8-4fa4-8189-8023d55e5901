
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, Maximize, SkipBack, SkipForward, RotateCcw, RotateCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface VideoControlsProps {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number[];
  playbackRate: number;
  canGoBack: boolean;
  canGoForward: boolean;
  onPlay: () => void;
  onPause: () => void;
  onSeek: (time: number) => void;
  onVolumeChange: (volume: number[]) => void;
  onPlaybackRateChange: (rate: number) => void;
  onSkipBack: () => void;
  onSkipForward: () => void;
  onFullscreen: () => void;
  onFrameBack: () => void;
  onFrameForward: () => void;
}

const formatTime = (seconds: number): string => {
  const hrs = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const frames = Math.floor((seconds % 1) * 30); // Assuming 30fps
  
  if (hrs > 0) {
    return `${hrs}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}:${frames.toString().padStart(2, '0')}`;
  }
  return `${mins}:${secs.toString().padStart(2, '0')}:${frames.toString().padStart(2, '0')}`;
};

export const VideoControls: React.FC<VideoControlsProps> = ({
  isPlaying,
  currentTime,
  duration,
  volume,
  playbackRate,
  canGoBack,
  canGoForward,
  onPlay,
  onPause,
  onSeek,
  onVolumeChange,
  onPlaybackRateChange,
  onSkipBack,
  onSkipForward,
  onFullscreen,
  onFrameBack,
  onFrameForward
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [lastVolume, setLastVolume] = useState(volume);
  const progressRef = useRef<HTMLDivElement>(null);

  const handleProgressMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    handleProgressClick(e);
  }, []);

  const handleProgressClick = useCallback((e: React.MouseEvent) => {
    if (!progressRef.current) return;
    
    const rect = progressRef.current.getBoundingClientRect();
    const percentage = (e.clientX - rect.left) / rect.width;
    const newTime = percentage * duration;
    onSeek(newTime);
  }, [duration, onSeek]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !progressRef.current) return;
    
    const rect = progressRef.current.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const newTime = percentage * duration;
    onSeek(newTime);
  }, [isDragging, duration, onSeek]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const toggleMute = useCallback(() => {
    if (isMuted) {
      onVolumeChange(lastVolume);
      setIsMuted(false);
    } else {
      setLastVolume(volume);
      onVolumeChange([0]);
      setIsMuted(true);
    }
  }, [isMuted, volume, lastVolume, onVolumeChange]);

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className="bg-gray-900/95 backdrop-blur-sm border-t border-gray-700 p-4">
      {/* Progress Bar */}
      <div 
        ref={progressRef}
        className="relative w-full h-2 bg-gray-700 rounded-full cursor-pointer mb-4 group"
        onMouseDown={handleProgressMouseDown}
        onClick={handleProgressClick}
      >
        <div 
          className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-150"
          style={{ width: `${progressPercentage}%` }}
        />
        <div 
          className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing"
          style={{ left: `calc(${progressPercentage}% - 8px)` }}
        />
      </div>

      <div className="flex items-center justify-between">
        {/* Left Controls */}
        <div className="flex items-center gap-3">
          {/* Transport Controls */}
          <div className="flex items-center gap-1 bg-gray-800 rounded-lg p-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-300 hover:text-white hover:bg-gray-700"
              onClick={onSkipBack}
              disabled={!canGoBack}
              title="Previous clip"
            >
              <SkipBack className="w-4 h-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-300 hover:text-white hover:bg-gray-700"
              onClick={onFrameBack}
              title="Previous frame"
            >
              <RotateCcw className="w-3 h-3" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="h-10 w-10 p-0 text-white hover:bg-gray-700"
              onClick={isPlaying ? onPause : onPlay}
              title={isPlaying ? "Pause" : "Play"}
            >
              {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-300 hover:text-white hover:bg-gray-700"
              onClick={onFrameForward}
              title="Next frame"
            >
              <RotateCw className="w-3 h-3" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-300 hover:text-white hover:bg-gray-700"
              onClick={onSkipForward}
              disabled={!canGoForward}
              title="Next clip"
            >
              <SkipForward className="w-4 h-4" />
            </Button>
          </div>

          {/* Time Display */}
          <div className="flex items-center gap-2 bg-gray-800 rounded-lg px-3 py-1">
            <span className="text-sm font-mono text-gray-300">
              {formatTime(currentTime)}
            </span>
            <span className="text-gray-500">/</span>
            <span className="text-sm font-mono text-gray-400">
              {formatTime(duration)}
            </span>
          </div>

          {/* Playback Speed */}
          <Select value={playbackRate.toString()} onValueChange={(value) => onPlaybackRateChange(parseFloat(value))}>
            <SelectTrigger className="w-20 h-8 bg-gray-800 border-gray-600 text-gray-300">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="0.25" className="text-gray-300 hover:bg-gray-700">0.25x</SelectItem>
              <SelectItem value="0.5" className="text-gray-300 hover:bg-gray-700">0.5x</SelectItem>
              <SelectItem value="1" className="text-gray-300 hover:bg-gray-700">1x</SelectItem>
              <SelectItem value="1.5" className="text-gray-300 hover:bg-gray-700">1.5x</SelectItem>
              <SelectItem value="2" className="text-gray-300 hover:bg-gray-700">2x</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Right Controls */}
        <div className="flex items-center gap-3">
          {/* Volume Control */}
          <div className="flex items-center gap-2 bg-gray-800 rounded-lg px-3 py-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-gray-300 hover:text-white"
              onClick={toggleMute}
            >
              {isMuted || volume[0] === 0 ? (
                <VolumeX className="w-4 h-4" />
              ) : (
                <Volume2 className="w-4 h-4" />
              )}
            </Button>
            <Slider
              value={isMuted ? [0] : volume}
              onValueChange={onVolumeChange}
              max={100}
              step={1}
              className="w-20"
            />
            <span className="text-xs text-gray-400 w-8">
              {isMuted ? 0 : volume[0]}%
            </span>
          </div>

          {/* Fullscreen */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-gray-300 hover:text-white hover:bg-gray-700"
            onClick={onFullscreen}
            title="Fullscreen"
          >
            <Maximize className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};
