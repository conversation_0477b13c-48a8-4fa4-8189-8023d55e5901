import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Upload, FileText, Download, Settings, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';
import { TranscriptParser } from '@/utils/transcriptParser';

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
}

interface TranscriptImporterProps {
  onImportSubtitles: (subtitles: Subtitle[]) => void;
  videoDuration?: number;
  onClose?: () => void;
}

export const TranscriptImporter: React.FC<TranscriptImporterProps> = ({
  onImportSubtitles,
  videoDuration,
  onClose
}) => {
  const [transcriptText, setTranscriptText] = useState('');
  const [selectedFormat, setSelectedFormat] = useState<string>('auto');
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewSubtitles, setPreviewSubtitles] = useState<Subtitle[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  
  // Advanced settings
  const [defaultDuration, setDefaultDuration] = useState([3]);
  const [maxCharsPerSubtitle, setMaxCharsPerSubtitle] = useState([80]);
  const [fontSize, setFontSize] = useState([24]);
  const [fontColor, setFontColor] = useState('#FFFFFF');
  const [backgroundColor, setBackgroundColor] = useState('rgba(0, 0, 0, 0.7)');
  const [position, setPosition] = useState<'top' | 'center' | 'bottom'>('bottom');
  const [fontFamily, setFontFamily] = useState('Arial, sans-serif');

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setTranscriptText(content);
      
      // Auto-detect format based on file extension
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (extension === 'srt') {
        setSelectedFormat('srt');
      } else if (extension === 'vtt' || extension === 'webvtt') {
        setSelectedFormat('vtt');
      } else if (extension === 'json') {
        setSelectedFormat('json');
      } else {
        setSelectedFormat('plain');
      }
      
      toast.success(`File "${file.name}" loaded successfully`);
    };
    
    reader.onerror = () => {
      toast.error('Failed to read file');
    };
    
    reader.readAsText(file);
  };

  const handleProcessTranscript = async () => {
    if (!transcriptText.trim()) {
      toast.error('Please enter or upload transcript content');
      return;
    }

    setIsProcessing(true);
    
    try {
      const parsedTranscript = TranscriptParser.parseTranscript(
        transcriptText,
        selectedFormat === 'auto' ? undefined : selectedFormat
      );

      const subtitles = TranscriptParser.convertToSubtitles(parsedTranscript, {
        videoDuration,
        defaultDuration: defaultDuration[0],
        maxCharsPerSubtitle: maxCharsPerSubtitle[0],
        fontSize: fontSize[0],
        fontColor,
        backgroundColor,
        position,
        fontFamily
      });

      setPreviewSubtitles(subtitles);
      setShowPreview(true);
      
      toast.success(`Generated ${subtitles.length} subtitles from transcript`);
    } catch (error) {
      toast.error('Failed to process transcript: ' + (error as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImportSubtitles = () => {
    if (previewSubtitles.length === 0) {
      toast.error('No subtitles to import');
      return;
    }

    onImportSubtitles(previewSubtitles);
    toast.success(`Imported ${previewSubtitles.length} subtitles`);
    onClose?.();
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = (seconds % 60).toFixed(1);
    return `${mins}:${secs.padStart(4, '0')}`;
  };

  return (
    <Card className="p-6 space-y-6 max-w-4xl mx-auto">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="w-5 h-5" />
          <h2 className="text-xl font-semibold">Import Transcript</h2>
        </div>
        {onClose && (
          <Button variant="ghost" onClick={onClose}>
            ×
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-4">
          <div>
            <Label className="text-sm font-medium">Transcript Content</Label>
            <div className="mt-2 space-y-2">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="flex items-center gap-2"
                >
                  <Upload className="w-4 h-4" />
                  Upload File
                </Button>
                <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">Auto</SelectItem>
                    <SelectItem value="plain">Plain Text</SelectItem>
                    <SelectItem value="srt">SRT</SelectItem>
                    <SelectItem value="vtt">VTT</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Textarea
                value={transcriptText}
                onChange={(e) => setTranscriptText(e.target.value)}
                placeholder="Paste your transcript here or upload a file..."
                className="min-h-[200px] font-mono text-sm"
              />
            </div>
          </div>

          {/* Advanced Settings */}
          <div>
            <Button
              variant="ghost"
              onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
              className="flex items-center gap-2 p-0 h-auto"
            >
              <Settings className="w-4 h-4" />
              Advanced Settings
              {showAdvancedSettings ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
            
            {showAdvancedSettings && (
              <div className="mt-4 space-y-4 p-4 border rounded-lg">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-xs">Default Duration (seconds)</Label>
                    <Slider
                      value={defaultDuration}
                      onValueChange={setDefaultDuration}
                      min={1}
                      max={10}
                      step={0.5}
                      className="mt-2"
                    />
                    <span className="text-xs text-gray-500">{defaultDuration[0]}s</span>
                  </div>
                  
                  <div>
                    <Label className="text-xs">Max Characters</Label>
                    <Slider
                      value={maxCharsPerSubtitle}
                      onValueChange={setMaxCharsPerSubtitle}
                      min={40}
                      max={120}
                      step={10}
                      className="mt-2"
                    />
                    <span className="text-xs text-gray-500">{maxCharsPerSubtitle[0]} chars</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-xs">Font Size</Label>
                    <Slider
                      value={fontSize}
                      onValueChange={setFontSize}
                      min={12}
                      max={48}
                      step={2}
                      className="mt-2"
                    />
                    <span className="text-xs text-gray-500">{fontSize[0]}px</span>
                  </div>
                  
                  <div>
                    <Label className="text-xs">Position</Label>
                    <Select value={position} onValueChange={(value: 'top' | 'center' | 'bottom') => setPosition(value)}>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="top">Top</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="bottom">Bottom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-xs">Font Color</Label>
                    <Input
                      type="color"
                      value={fontColor}
                      onChange={(e) => setFontColor(e.target.value)}
                      className="mt-2 h-8"
                    />
                  </div>
                  
                  <div>
                    <Label className="text-xs">Font Family</Label>
                    <Select value={fontFamily} onValueChange={setFontFamily}>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                        <SelectItem value="Helvetica, sans-serif">Helvetica</SelectItem>
                        <SelectItem value="Times New Roman, serif">Times New Roman</SelectItem>
                        <SelectItem value="Courier New, monospace">Courier New</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleProcessTranscript}
              disabled={isProcessing || !transcriptText.trim()}
              className="flex-1"
            >
              {isProcessing ? 'Processing...' : 'Generate Subtitles'}
            </Button>
          </div>
        </div>

        {/* Preview Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Preview</Label>
            {previewSubtitles.length > 0 && (
              <span className="text-xs text-gray-500">
                {previewSubtitles.length} subtitles
              </span>
            )}
          </div>
          
          {showPreview && previewSubtitles.length > 0 ? (
            <div className="space-y-2">
              <div className="max-h-[300px] overflow-y-auto space-y-2 border rounded-lg p-2">
                {previewSubtitles.slice(0, 10).map((subtitle, index) => (
                  <div key={subtitle.id} className="p-2 bg-gray-50 rounded text-sm">
                    <div className="flex justify-between items-start">
                      <span className="text-xs text-gray-500">
                        {formatTime(subtitle.startTime)} → {formatTime(subtitle.endTime)}
                      </span>
                      <span className="text-xs text-gray-400">#{index + 1}</span>
                    </div>
                    <div className="mt-1 text-gray-800">{subtitle.text}</div>
                  </div>
                ))}
                {previewSubtitles.length > 10 && (
                  <div className="text-center text-xs text-gray-500 py-2">
                    ... and {previewSubtitles.length - 10} more subtitles
                  </div>
                )}
              </div>
              
              <Button
                onClick={handleImportSubtitles}
                className="w-full"
                variant="default"
              >
                <Download className="w-4 h-4 mr-2" />
                Import {previewSubtitles.length} Subtitles
              </Button>
            </div>
          ) : (
            <div className="h-[300px] border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center text-gray-500">
              <div className="text-center">
                <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Subtitle preview will appear here</p>
                <p className="text-sm">Process your transcript to see the preview</p>
              </div>
            </div>
          )}
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept=".txt,.srt,.vtt,.webvtt,.json"
        onChange={handleFileUpload}
        className="hidden"
      />
    </Card>
  );
};
