import React from 'react';
import { Settings, Video, Type as TypeI<PERSON>, Sliders } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { SubtitleEditor } from './SubtitleEditor';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
  sourceStartTime?: number;
  sourceEndTime?: number;
  muted?: boolean;
}

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
  wordsPerChunk?: number;
}

interface InspectorPanelProps {
  selectedClips: string[];
  selectedSubtitles: string[];
  videoClips: VideoClip[];
  subtitles: Subtitle[];
  currentTime: number;
  canvasSize: '16:9' | '9:16' | '1:1' | '4:3';
  onCanvasSizeChange: (size: '16:9' | '9:16' | '1:1' | '4:3') => void;
  onAddZoomEffect: (zoomLevel: number) => void;
  onAddSubtitle: () => void;
  onUpdateSubtitle: (id: string, updates: Partial<Subtitle>) => void;
  onDeleteSubtitle: (id: string) => void;
  onSelectSubtitle: (id: string) => void;
  onImportSubtitles?: (subtitles: Subtitle[]) => void;
  onDeleteAllSubtitles?: () => void;
  onAddPastedSubtitle?: (text: string, wordsPerChunk: number) => void;
  getTimelineDuration: () => number;
}

export const InspectorPanel: React.FC<InspectorPanelProps> = ({
  selectedClips,
  selectedSubtitles,
  videoClips,
  subtitles,
  currentTime,
  canvasSize,
  onCanvasSizeChange,
  onAddZoomEffect,
  onAddSubtitle,
  onUpdateSubtitle,
  onDeleteSubtitle,
  onSelectSubtitle,
  onImportSubtitles,
  onDeleteAllSubtitles,
  onAddPastedSubtitle,
  getTimelineDuration
}) => {
  const selectedClip = selectedClips.length > 0 ? videoClips.find(clip => clip.id === selectedClips[0]) : null;
  const selectedSubtitle = selectedSubtitles.length > 0 ? subtitles.find(sub => sub.id === selectedSubtitles[0]) : null;

  return (
    <div className="h-full bg-gray-800 border-l border-gray-700 flex flex-col overflow-y-auto">
      <div className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <Settings className="w-5 h-5 text-gray-400" />
          <h3 className="font-semibold text-gray-200">Inspector</h3>
        </div>
        
        {/* Video Clip Properties */}
        {selectedClips.length > 0 && selectedClip && (
          <div className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Video className="w-4 h-4 text-blue-400" />
                <h4 className="text-sm font-medium text-gray-300">Video Clip</h4>
              </div>
              
              <div className="space-y-3">
                <div>
                  <Label className="text-xs text-gray-400">Clip Name</Label>
                  <div className="text-sm text-white font-medium truncate">{selectedClip.name}</div>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs text-gray-400">Duration</Label>
                    <div className="text-sm text-white">
                      {Math.floor(selectedClip.endTime - selectedClip.startTime)}s
                    </div>
                  </div>
                  <div>
                    <Label className="text-xs text-gray-400">Position</Label>
                    <div className="text-sm text-white">
                      {Math.floor(selectedClip.startTime)}s
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Transform Controls */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Sliders className="w-4 h-4 text-green-400" />
                <h4 className="text-sm font-medium text-gray-300">Transform</h4>
              </div>
              
              <div className="space-y-3">
                <div>
                  <Label className="text-xs text-gray-400 mb-2 block">Zoom Effects</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 text-xs"
                      onClick={() => onAddZoomEffect(150)}
                    >
                      150%
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 text-xs"
                      onClick={() => onAddZoomEffect(200)}
                    >
                      200%
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 text-xs"
                      onClick={() => onAddZoomEffect(300)}
                    >
                      300%
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 text-xs"
                      onClick={() => onAddZoomEffect(100)}
                    >
                      Reset
                    </Button>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Canvas Size */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-300">Canvas Size</h4>
              <div className="space-y-2">
                <Label className="text-xs text-gray-400">Aspect Ratio</Label>
                <Select
                  value={canvasSize}
                  onValueChange={onCanvasSizeChange}
                >
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    <SelectItem value="16:9">16:9 Landscape</SelectItem>
                    <SelectItem value="9:16">9:16 Portrait</SelectItem>
                    <SelectItem value="1:1">1:1 Square</SelectItem>
                    <SelectItem value="4:3">4:3 Traditional</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500">
                  {canvasSize === '16:9' && 'YouTube, TV'}
                  {canvasSize === '9:16' && 'TikTok, Stories'}
                  {canvasSize === '1:1' && 'Instagram Posts'}
                  {canvasSize === '4:3' && 'Traditional TV'}
                </p>
              </div>
            </div>
          </div>
        )}
        
        {/* Subtitle Properties */}
        {selectedSubtitles.length > 0 && (
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <TypeIcon className="w-4 h-4 text-purple-400" />
              <h4 className="text-sm font-medium text-gray-300">Subtitle Properties</h4>
            </div>
            
            <SubtitleEditor
              subtitles={subtitles}
              selectedSubtitles={selectedSubtitles}
              currentTime={currentTime}
              videoDuration={getTimelineDuration()}
              onAddSubtitle={onAddSubtitle}
              onUpdateSubtitle={onUpdateSubtitle}
              onDeleteSubtitle={onDeleteSubtitle}
              onSelectSubtitle={onSelectSubtitle}
              onImportSubtitles={onImportSubtitles}
              onDeleteAllSubtitles={onDeleteAllSubtitles}
              onAddPastedSubtitle={onAddPastedSubtitle}
            />
          </div>
        )}
        
        {/* Empty State */}
        {selectedClips.length === 0 && selectedSubtitles.length === 0 && (
          <div className="text-center text-gray-400 py-12">
            <Settings className="w-16 h-16 mx-auto mb-4 opacity-30" />
            <p className="text-sm mb-2">No Selection</p>
            <p className="text-xs text-gray-500">
              Select an element in the timeline to see its properties and controls
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
