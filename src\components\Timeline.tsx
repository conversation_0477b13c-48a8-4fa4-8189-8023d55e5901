import React, { useCallback, useRef, useEffect, useState } from 'react';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { <PERSON>rid, Eye, EyeOff, Maximize2, RotateCcw, Volume2, VolumeX, Scissors, Trash2, Move } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { WaveformCanvas } from './WaveformCanvas';
import { TimelineRuler } from './timeline/TimelineRuler';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
  sourceStartTime?: number;
  sourceEndTime?: number;
}

interface ZoomEffect {
  id: string;
  startTime: number;
  endTime: number;
  zoomLevel: number;
  clipId: string;
}

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
  wordsPerChunk?: number; // For pasted subtitles with chunking
}

interface TimelineProps {
  videoClips: VideoClip[];
  zoomEffects: ZoomEffect[];
  subtitles?: Subtitle[];
  selectedSubtitles?: string[];
  currentTime: number;
  selectedClips: string[];
  onTimeSeek: (time: number) => void;
  onClipSelect: (clipId: string, isMultiSelect?: boolean) => void;
  onClipTrim: (clipId: string, startTime: number, endTime: number) => void;
  onClipMove: (clipId: string, newStartTime: number) => void;
  onClipMute: (clipId: string, muted: boolean) => void;
  onZoomEffectMove: (effectId: string, newStartTime: number) => void;
  onZoomEffectDelete: (effectId: string) => void;
  onZoomEffectTrim: (effectId: string, startTime: number, endTime: number) => void;
  onSubtitleSelect?: (subtitleId: string) => void;
  onSubtitleMove?: (subtitleId: string, newStartTime: number) => void;
  onSubtitleTrim?: (subtitleId: string, startTime: number, endTime: number) => void;
  zoom: number;
  onZoomChange: (zoom: number) => void;
  // Timeline action handlers
  onCutClip?: () => void;
  onCopyClip?: () => void;
  onDeleteClip?: () => void;
}

type TimeDisplayUnit = 'seconds' | 'frames' | 'timecode';

export const Timeline: React.FC<TimelineProps> = ({
  videoClips,
  zoomEffects,
  subtitles = [],
  selectedSubtitles = [],
  currentTime,
  selectedClips,
  onTimeSeek,
  onClipSelect,
  onClipTrim,
  onClipMove,
  onClipMute,
  onZoomEffectMove,
  onZoomEffectDelete,
  onZoomEffectTrim,
  onSubtitleSelect,
  onSubtitleMove,
  onSubtitleTrim,
  zoom,
  onZoomChange,
  onCutClip,
  onCopyClip,
  onDeleteClip
}) => {
  const timelineRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<string | null>(null);
  const [isResizing, setIsResizing] = useState<{ clipId: string; handle: 'start' | 'end'; type: 'clip' | 'zoom' } | null>(null);
  const [dragOffset, setDragOffset] = useState(0);
  const [snapLines, setSnapLines] = useState<number[]>([]);
  const [showSnapLines, setShowSnapLines] = useState(false);
  const [showGrid, setShowGrid] = useState(true);
  const [showWaveforms, setShowWaveforms] = useState(true);
  const [timeDisplayUnit, setTimeDisplayUnit] = useState<TimeDisplayUnit>('seconds');
  const [magneticSnapping, setMagneticSnapping] = useState(true);
  const [trimPreview, setTrimPreview] = useState<{ clipId: string; startTime: number; endTime: number; type: 'clip' | 'zoom' } | null>(null);

  // Safe duration and width calculations with validation
  const totalDuration = Math.max(...videoClips.map(clip => clip.endTime), 60);
  const isValidDuration = !isNaN(totalDuration) && isFinite(totalDuration) && totalDuration > 0;
  const timelineWidth = isValidDuration ? (totalDuration * zoom) / 100 * 20 : 800;
  const playheadPosition = isValidDuration ? (currentTime / totalDuration) * timelineWidth : 0;
  
  const snapThreshold = magneticSnapping ? 15 : 5;
  const gridInterval = 1;
  const magneticRange = 20;

  const getClipColor = (clip: VideoClip) => {
    const extension = clip.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'mp4':
      case 'mov':
      case 'avi':
        return 'bg-blue-600';
      case 'mp3':
      case 'wav':
      case 'aac':
        return 'bg-green-600';
      case 'jpg':
      case 'png':
      case 'gif':
        return 'bg-purple-600';
      default:
        return 'bg-blue-600';
    }
  };

  const formatTime = (seconds: number) => {
    switch (timeDisplayUnit) {
      case 'frames':
        const frames = Math.floor(seconds * 30);
        return `${frames}f`;
      case 'timecode':
        const hrs = Math.floor(seconds / 3600);
        const mins = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        const frameNum = Math.floor((seconds % 1) * 30);
        return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}:${frameNum.toString().padStart(2, '0')}`;
      default:
        const mins2 = Math.floor(seconds / 60);
        const secs2 = Math.floor(seconds % 60);
        return `${mins2}:${secs2.toString().padStart(2, '0')}`;
    }
  };

  const fitToWindow = useCallback(() => {
    if (totalDuration > 0) {
      const containerWidth = 800;
      const optimalZoom = Math.max(25, Math.min(400, (containerWidth / totalDuration) * 5));
      onZoomChange(optimalZoom);
    }
  }, [totalDuration, onZoomChange]);

  const getClipWidth = (clip: VideoClip) => {
    if (trimPreview && trimPreview.clipId === clip.id && trimPreview.type === 'clip') {
      return ((trimPreview.endTime - trimPreview.startTime) / totalDuration) * timelineWidth;
    }
    return ((clip.endTime - clip.startTime) / totalDuration) * timelineWidth;
  };

  const getClipPosition = (clip: VideoClip) => {
    if (trimPreview && trimPreview.clipId === clip.id && trimPreview.type === 'clip') {
      return (trimPreview.startTime / totalDuration) * timelineWidth;
    }
    return (clip.startTime / totalDuration) * timelineWidth;
  };

  const getEffectWidth = (effect: ZoomEffect) => {
    if (trimPreview && trimPreview.clipId === effect.id && trimPreview.type === 'zoom') {
      return ((trimPreview.endTime - trimPreview.startTime) / totalDuration) * timelineWidth;
    }
    return ((effect.endTime - effect.startTime) / totalDuration) * timelineWidth;
  };

  const getEffectPosition = (effect: ZoomEffect) => {
    if (trimPreview && trimPreview.clipId === effect.id && trimPreview.type === 'zoom') {
      return (trimPreview.startTime / totalDuration) * timelineWidth;
    }
    return (effect.startTime / totalDuration) * timelineWidth;
  };

  const getSnapPoints = useCallback(() => {
    const points: number[] = [];
    
    for (let i = 0; i <= totalDuration; i += gridInterval) {
      points.push(i);
    }
    
    videoClips.forEach(clip => {
      points.push(clip.startTime);
      points.push(clip.endTime);
    });

    zoomEffects.forEach(effect => {
      points.push(effect.startTime);
      points.push(effect.endTime);
    });
    
    subtitles.forEach(subtitle => {
      points.push(subtitle.startTime);
      points.push(subtitle.endTime);
    });
    
    points.push(currentTime);
    
    return points.sort((a, b) => a - b);
  }, [videoClips, zoomEffects, subtitles, currentTime, totalDuration]);

  const findNearestSnap = useCallback((time: number) => {
    if (!magneticSnapping) return time;
    
    const snapPoints = getSnapPoints();
    const timelineX = (time / totalDuration) * timelineWidth;
    let closestSnap = time;
    let closestDistance = Infinity;
    
    for (const snapPoint of snapPoints) {
      const snapX = (snapPoint / totalDuration) * timelineWidth;
      const distance = Math.abs(timelineX - snapX);
      
      if (distance <= magneticRange && distance < closestDistance) {
        closestDistance = distance;
        closestSnap = snapPoint;
      }
    }
    
    return closestSnap;
  }, [getSnapPoints, totalDuration, timelineWidth, magneticRange, magneticSnapping]);

  const updateSnapLines = useCallback((time: number) => {
    if (!magneticSnapping) {
      setShowSnapLines(false);
      return;
    }
    
    const snapPoints = getSnapPoints();
    const timelineX = (time / totalDuration) * timelineWidth;
    const lines: number[] = [];
    
    for (const snapPoint of snapPoints) {
      const snapX = (snapPoint / totalDuration) * timelineWidth;
      if (Math.abs(timelineX - snapX) <= magneticRange) {
        lines.push(snapX);
      }
    }
    
    setSnapLines(lines);
    setShowSnapLines(lines.length > 0);
  }, [getSnapPoints, totalDuration, timelineWidth, magneticRange, magneticSnapping]);

  const handleClipMouseDown = useCallback((e: React.MouseEvent, clipId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    const isMultiSelect = e.ctrlKey || e.metaKey;
    
    if (!selectedClips.includes(clipId)) {
      onClipSelect(clipId, isMultiSelect);
    }
    
    const rect = timelineRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const clip = videoClips.find(c => c.id === clipId);
    if (!clip) return;
    
    const clickX = e.clientX - rect.left;
    const clipX = getClipPosition(clip);
    
    setIsDragging(clipId);
    setDragOffset(clickX - clipX);
  }, [selectedClips, onClipSelect, videoClips, getClipPosition]);

  const handleZoomMouseDown = useCallback((e: React.MouseEvent, effectId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    const isMultiSelect = e.ctrlKey || e.metaKey;
    
    if (!selectedClips.includes(effectId)) {
      onClipSelect(effectId, isMultiSelect);
    }
    
    const rect = timelineRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const effect = zoomEffects.find(e => e.id === effectId);
    if (!effect) return;
    
    const clickX = e.clientX - rect.left;
    const effectX = getEffectPosition(effect);
    
    setIsDragging(effectId);
    setDragOffset(clickX - effectX);
  }, [selectedClips, onClipSelect, zoomEffects, getEffectPosition]);

  const handleSubtitleMouseDown = useCallback((e: React.MouseEvent, subtitleId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onSubtitleSelect) {
      onSubtitleSelect(subtitleId);
    }
    
    const rect = timelineRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const subtitle = subtitles.find(s => s.id === subtitleId);
    if (!subtitle) return;
    
    const clickX = e.clientX - rect.left;
    const subtitleX = getSubtitlePosition(subtitle);
    
    setIsDragging(subtitleId);
    setDragOffset(clickX - subtitleX);
  }, [onSubtitleSelect, subtitles]);

  const getSubtitlePosition = (subtitle: Subtitle) => {
    return (subtitle.startTime / totalDuration) * timelineWidth;
  };

  const getSubtitleWidth = (subtitle: Subtitle) => {
    return ((subtitle.endTime - subtitle.startTime) / totalDuration) * timelineWidth;
  };

  const handleResizeMouseDown = useCallback((e: React.MouseEvent, clipId: string, handle: 'start' | 'end', type: 'clip' | 'zoom' | 'subtitle' = 'clip') => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsResizing({ clipId, handle, type });
  }, []);

  const handleSubtitleResizeMouseDown = useCallback((e: React.MouseEvent, subtitleId: string, handle: 'start' | 'end') => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsResizing({ clipId: subtitleId, handle, type: 'subtitle' });
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!timelineRef.current) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseTime = (mouseX / timelineWidth) * totalDuration;
    
    if (isDragging) {
      const clip = videoClips.find(c => c.id === isDragging);
      const effect = zoomEffects.find(e => e.id === isDragging);
      const subtitle = subtitles.find(s => s.id === isDragging);
      
      if (clip) {
        const newStartTime = ((mouseX - dragOffset) / timelineWidth) * totalDuration;
        const snappedStartTime = findNearestSnap(newStartTime);
        const constrainedStartTime = Math.max(0, Math.min(totalDuration - (clip.endTime - clip.startTime), snappedStartTime));
        
        updateSnapLines(constrainedStartTime);
        
        if (selectedClips.includes(isDragging)) {
          const offset = constrainedStartTime - clip.startTime;
          selectedClips.forEach(clipId => {
            const targetClip = videoClips.find(c => c.id === clipId);
            if (targetClip) {
              const newStart = Math.max(0, targetClip.startTime + offset);
              onClipMove(clipId, newStart);
            }
          });
        } else {
          onClipMove(isDragging, constrainedStartTime);
        }
      } else if (effect) {
        const newStartTime = ((mouseX - dragOffset) / timelineWidth) * totalDuration;
        const snappedStartTime = findNearestSnap(newStartTime);
        const effectDuration = effect.endTime - effect.startTime;
        const constrainedStartTime = Math.max(0, Math.min(totalDuration - effectDuration, snappedStartTime));
        
        updateSnapLines(constrainedStartTime);
        
        const associatedClip = videoClips.find(c => c.id === effect.clipId);
        if (associatedClip) {
          const clampedStartTime = Math.max(associatedClip.startTime, Math.min(associatedClip.endTime - effectDuration, constrainedStartTime));
          onZoomEffectMove(effect.id, clampedStartTime);
        }
      } else if (subtitle && onSubtitleMove) {
        const newStartTime = ((mouseX - dragOffset) / timelineWidth) * totalDuration;
        const snappedStartTime = findNearestSnap(newStartTime);
        const subtitleDuration = subtitle.endTime - subtitle.startTime;
        const constrainedStartTime = Math.max(0, Math.min(totalDuration - subtitleDuration, snappedStartTime));
        
        updateSnapLines(constrainedStartTime);
        onSubtitleMove(isDragging, constrainedStartTime);
      }
    }
    
    if (isResizing) {
      const { clipId, handle, type } = isResizing;
      
      if (type === 'clip') {
        const clip = videoClips.find(c => c.id === clipId);
        if (!clip) return;
        
        const snappedTime = findNearestSnap(mouseTime);
        updateSnapLines(snappedTime);
        
        if (handle === 'start') {
          const newStartTime = Math.max(0, Math.min(clip.endTime - 0.1, snappedTime));
          setTrimPreview({ clipId, startTime: newStartTime, endTime: clip.endTime, type: 'clip' });
        } else {
          const newEndTime = Math.max(clip.startTime + 0.1, Math.min(totalDuration, snappedTime));
          setTrimPreview({ clipId, startTime: clip.startTime, endTime: newEndTime, type: 'clip' });
        }
      } else if (type === 'zoom') {
        const effect = zoomEffects.find(e => e.id === clipId);
        if (!effect) return;
        
        const snappedTime = findNearestSnap(mouseTime);
        updateSnapLines(snappedTime);
        
        const associatedClip = videoClips.find(c => c.id === effect.clipId);
        if (associatedClip) {
          if (handle === 'start') {
            const newStartTime = Math.max(associatedClip.startTime, Math.min(effect.endTime - 0.1, snappedTime));
            setTrimPreview({ clipId, startTime: newStartTime, endTime: effect.endTime, type: 'zoom' });
          } else {
            const newEndTime = Math.max(effect.startTime + 0.1, Math.min(associatedClip.endTime, snappedTime));
            setTrimPreview({ clipId, startTime: effect.startTime, endTime: newEndTime, type: 'zoom' });
          }
        }
      } else if (type === 'subtitle') {
        const subtitle = subtitles.find(s => s.id === clipId);
        if (!subtitle) return;
        
        const snappedTime = findNearestSnap(mouseTime);
        updateSnapLines(snappedTime);
        
        if (handle === 'start') {
          const newStartTime = Math.max(0, Math.min(subtitle.endTime - 0.1, snappedTime));
          setTrimPreview({ clipId, startTime: newStartTime, endTime: subtitle.endTime, type: 'subtitle' });
        } else {
          const newEndTime = Math.max(subtitle.startTime + 0.1, Math.min(totalDuration, snappedTime));
          setTrimPreview({ clipId, startTime: subtitle.startTime, endTime: newEndTime, type: 'subtitle' });
        }
      }
    }
  }, [isDragging, isResizing, timelineWidth, totalDuration, dragOffset, videoClips, zoomEffects, selectedClips, onClipMove, onZoomEffectMove, findNearestSnap, updateSnapLines]);

  const handleMouseUp = useCallback(() => {
    if (isResizing && trimPreview) {
      if (trimPreview.type === 'clip') {
        onClipTrim(trimPreview.clipId, trimPreview.startTime, trimPreview.endTime);
      } else if (trimPreview.type === 'zoom') {
        onZoomEffectTrim(trimPreview.clipId, trimPreview.startTime, trimPreview.endTime);
      } else if (trimPreview.type === 'subtitle' && onSubtitleTrim) {
        onSubtitleTrim(trimPreview.clipId, trimPreview.startTime, trimPreview.endTime);
      }
    }
    
    setIsDragging(null);
    setIsResizing(null);
    setTrimPreview(null);
    setShowSnapLines(false);
    setSnapLines([]);
  }, [isResizing, trimPreview, onClipTrim, onZoomEffectTrim, onSubtitleTrim]);

  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  // Safe calculation for grid lines count
  const safeGridCount = isValidDuration ? Math.min(Math.ceil(totalDuration / 5) + 1, 1000) : 0;

  return (
    <div className="h-96 video-editor-bg border-t border-gray-700 font-inter">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 video-editor-panel border-b border-gray-700">
          <div className="flex items-center gap-4">
            <h3 className="font-bold text-xl text-white">Timeline</h3>

            {/* Timeline Action Tools */}
            <div className="flex items-center gap-2 ml-6">
              <Button
                variant="ghost"
                size="sm"
                className="p-2 rounded-lg transition-all duration-200 text-gray-400 hover:text-white hover:bg-gray-700"
                onClick={onCutClip}
                disabled={selectedClips.length === 0}
                title="Split clip at playhead (C)"
              >
                <Scissors className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="p-2 rounded-lg transition-all duration-200 text-gray-400 hover:text-white hover:bg-gray-700"
                onClick={onCopyClip}
                disabled={selectedClips.length === 0}
                title="Copy selected clip (Ctrl+C)"
              >
                <Move className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="p-2 rounded-lg transition-all duration-200 text-gray-400 hover:text-red-400 hover:bg-red-400/20"
                onClick={onDeleteClip}
                disabled={selectedClips.length === 0}
                title="Delete selected clip (Delete)"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                className={`p-2 rounded-lg transition-all duration-200 ${
                  showGrid 
                    ? 'text-editor-teal bg-editor-teal/20 hover:bg-editor-teal/30' 
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
                onClick={() => setShowGrid(!showGrid)}
                title="Toggle grid"
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`p-2 rounded-lg transition-all duration-200 ${
                  showWaveforms 
                    ? 'text-green-400 bg-green-400/20 hover:bg-green-400/30' 
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
                onClick={() => setShowWaveforms(!showWaveforms)}
                title="Toggle waveforms"
              >
                {showWaveforms ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`p-2 rounded-lg transition-all duration-200 ${
                  magneticSnapping 
                    ? 'text-yellow-400 bg-yellow-400/20 hover:bg-yellow-400/30' 
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
                onClick={() => setMagneticSnapping(!magneticSnapping)}
                title="Toggle magnetic snapping"
              >
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>

            <Select value={timeDisplayUnit} onValueChange={(value) => setTimeDisplayUnit(value as TimeDisplayUnit)}>
              <SelectTrigger className="w-24 h-8 bg-gray-700 border-gray-600 text-gray-300 text-sm rounded-lg">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600 rounded-lg">
                <SelectItem value="seconds" className="text-gray-300 hover:bg-gray-700 text-sm rounded">Seconds</SelectItem>
                <SelectItem value="frames" className="text-gray-300 hover:bg-gray-700 text-sm rounded">Frames</SelectItem>
                <SelectItem value="timecode" className="text-gray-300 hover:bg-gray-700 text-sm rounded">Timecode</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-300">Zoom:</span>
              <Slider
                value={[zoom]}
                onValueChange={(value) => onZoomChange(value[0])}
                max={400}
                min={25}
                step={25}
                className="w-24"
              />
              <span className="text-sm font-mono text-gray-300 w-14">{zoom}%</span>
              <Button
                variant="ghost"
                size="sm"
                className="p-2 text-gray-400 hover:text-editor-teal hover:bg-gray-700 rounded-lg transition-all duration-200"
                onClick={fitToWindow}
                title="Fit to window"
              >
                <Maximize2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
        
        {/* Single Scrollable Timeline Container */}
        <div className="flex-1 overflow-auto">
          <div className="p-6 space-y-4" style={{ minWidth: Math.max(timelineWidth, 800) }}>
            {/* Integrated Time Ruler */}
            <TimelineRuler
              totalDuration={totalDuration}
              timelineWidth={timelineWidth}
              currentTime={currentTime}
              onTimeSeek={onTimeSeek}
              showGrid={showGrid}
            />
            
            <div className="space-y-3">
              {/* Enhanced Video Track with Thumbnails */}
              <div className="relative">
                <div className="flex items-center gap-4 mb-2">
                  <div className="w-20 flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded"></div>
                    <span className="text-sm font-semibold text-white">Video</span>
                  </div>
                  <div className="text-xs text-gray-400">
                    {videoClips.length} clip{videoClips.length !== 1 ? 's' : ''}
                  </div>
                </div>
                <div className="timeline-track h-20 rounded-xl relative flex items-center" style={{ width: Math.max(timelineWidth, 800) }}>
                  <div
                    ref={timelineRef}
                    className="absolute inset-0 cursor-pointer rounded-xl"
                    onClick={(e) => {
                      if (!isDragging && !isResizing) {
                        const rect = e.currentTarget.getBoundingClientRect();
                        const clickX = e.clientX - rect.left;
                        const clickTime = (clickX / timelineWidth) * totalDuration;
                        onTimeSeek(Math.max(0, Math.min(totalDuration, clickTime)));
                      }
                    }}
                  />
                  
                  {/* Grid lines with safe array generation */}
                  {showGrid && isValidDuration && safeGridCount > 0 && (
                    <div className="absolute inset-0 pointer-events-none rounded-xl overflow-hidden">
                      {Array.from({ length: safeGridCount }, (_, i) => (
                        <div
                          key={i}
                          className="absolute top-0 bottom-0 w-px bg-white/10"
                          style={{ left: (i * 5 / totalDuration) * timelineWidth }}
                        />
                      ))}
                    </div>
                  )}
                  
                  {/* Enhanced video clips with thumbnails */}
                  {videoClips.map(clip => (
                    <div
                      key={clip.id}
                      className={`absolute h-14 clip-video rounded-lg mx-1 overflow-hidden cursor-move transition-all duration-200 group shadow-lg hover:shadow-xl ${
              selectedClips.includes(clip.id) ? 'ring-2 ring-editor-teal shadow-editor-teal/25' : ''
            } ${isDragging === clip.id ? 'opacity-75 scale-105' : ''} ${
              trimPreview && trimPreview.clipId === clip.id && trimPreview.type === 'clip' ? 'opacity-75' : ''
            }`}
                      style={{ 
                        left: getClipPosition(clip),
                        width: getClipWidth(clip),
                        minWidth: '80px',
                        top: '12px'
                      }}
                      onMouseDown={(e) => handleClipMouseDown(e, clip.id)}
                    >
                      {/* Video thumbnail background */}
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/80 to-blue-500/80">
                        <video
                          src={clip.url}
                          className="w-full h-full object-cover opacity-60"
                          muted
                          preload="metadata"
                        />
                        <div className="absolute inset-0 bg-black/20" />
                      </div>
                      
                      {/* Left resize handle */}
                      <div
                        className={`absolute left-0 top-0 bottom-0 w-2 bg-white/80 cursor-ew-resize transition-all duration-200 rounded-l-lg z-10 ${
                          isResizing?.clipId === clip.id && isResizing?.handle === 'start' && isResizing?.type === 'clip'
                            ? 'opacity-100 bg-editor-teal' 
                            : 'opacity-0 group-hover:opacity-100'
                        }`}
                        onMouseDown={(e) => handleResizeMouseDown(e, clip.id, 'start', 'clip')}
                      />
                      
                      {/* Clip name */}
                      <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-5">
                        <span className="text-sm text-white font-medium truncate px-2 text-center bg-black/30 rounded">
                          {clip.name}
                        </span>
                      </div>
                      
                      {/* Mute button */}
                      <Button
                        size="sm"
                        variant="ghost"
                        className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-auto z-10"
                        onClick={(e) => {
                          e.stopPropagation();
                          onClipMute(clip.id, !clip.muted);
                        }}
                      >
                        {clip.muted ? (
                          <VolumeX className="h-3 w-3 text-red-400" />
                        ) : (
                          <Volume2 className="h-3 w-3 text-white" />
                        )}
                      </Button>
                      
                      {/* Right resize handle */}
                      <div
                        className={`absolute right-0 top-0 bottom-0 w-2 bg-white/80 cursor-ew-resize transition-all duration-200 rounded-r-lg z-10 ${
                          isResizing?.clipId === clip.id && isResizing?.handle === 'end' && isResizing?.type === 'clip'
                            ? 'opacity-100 bg-editor-teal' 
                            : 'opacity-0 group-hover:opacity-100'
                        }`}
                        onMouseDown={(e) => handleResizeMouseDown(e, clip.id, 'end', 'clip')}
                      />
                    </div>
                  ))}
                  
                  {/* Snap lines */}
                  {showSnapLines && snapLines.map((lineX, index) => (
                    <div
                      key={index}
                      className="absolute top-0 bottom-0 w-0.5 bg-yellow-400 opacity-80 pointer-events-none z-30 shadow-lg animate-pulse-subtle"
                      style={{ left: lineX }}
                    />
                  ))}
                  
                  {/* Playhead */}
                  <div
                    className="absolute top-0 bottom-0 w-1 scrubber-handle z-20 pointer-events-none"
                    style={{ left: playheadPosition }}
                  >
                    <div className="absolute -top-3 -left-2 w-5 h-5 bg-editor-teal rounded-full shadow-lg"></div>
                  </div>
                </div>
              </div>

              {/* Subtitle Track */}
              <div className="relative">
                <div className="flex items-center gap-4 mb-2">
                  <div className="w-20 flex items-center gap-2">
                    <div className="w-3 h-3 bg-purple-500 rounded"></div>
                    <span className="text-sm font-semibold text-white">Text</span>
                  </div>
                  <div className="text-xs text-gray-400">
                    {subtitles.length} subtitle{subtitles.length !== 1 ? 's' : ''}
                  </div>
                </div>
                <div className="timeline-track h-20 rounded-xl relative flex items-center" style={{ width: Math.max(timelineWidth, 800) }}>
                  {subtitles.map((subtitle) => (
                    <div
                      key={subtitle.id}
                      className={`group absolute top-2 bottom-2 bg-blue-600 rounded-lg cursor-pointer transition-all duration-200 hover:brightness-110 ${
                        selectedSubtitles?.includes(subtitle.id) ? 'ring-4 ring-yellow-400 shadow-xl shadow-yellow-400/50 brightness-125 scale-105' : ''
                      } ${
                        isDragging === subtitle.id ? 'opacity-75 scale-105' : ''
                      } ${
                        trimPreview && trimPreview.clipId === subtitle.id && trimPreview.type === 'subtitle' ? 'opacity-75' : ''
                      }`}
                      style={{
                        left: getSubtitlePosition(subtitle),
                        width: getSubtitleWidth(subtitle),
                        minWidth: '40px'
                      }}
                      onMouseDown={(e) => handleSubtitleMouseDown(e, subtitle.id)}
                      title={subtitle.text}
                    >
                      <div className="absolute inset-0 flex items-center px-2">
                        <span className="text-white text-xs font-medium truncate">{subtitle.text}</span>
                      </div>
                      
                      {/* Resize handles */}
                      <div
                        className={`absolute left-0 top-0 bottom-0 w-2 bg-white/20 hover:bg-white/40 cursor-ew-resize rounded-l-lg transition-all duration-200 ${
                          isResizing?.clipId === subtitle.id && isResizing?.handle === 'start' && isResizing?.type === 'subtitle'
                            ? 'opacity-100 bg-editor-blue' 
                            : 'opacity-0 group-hover:opacity-100'
                        }`}
                        onMouseDown={(e) => handleSubtitleResizeMouseDown(e, subtitle.id, 'start')}
                      />
                      <div
                        className={`absolute right-0 top-0 bottom-0 w-2 bg-white/20 hover:bg-white/40 cursor-ew-resize rounded-r-lg transition-all duration-200 ${
                          isResizing?.clipId === subtitle.id && isResizing?.handle === 'end' && isResizing?.type === 'subtitle'
                            ? 'opacity-100 bg-editor-blue' 
                            : 'opacity-0 group-hover:opacity-100'
                        }`}
                        onMouseDown={(e) => handleSubtitleResizeMouseDown(e, subtitle.id, 'end')}
                      />
                    </div>
                  ))}
                  
                  {/* Playhead */}
                  <div
                    className="absolute top-0 bottom-0 w-1 scrubber-handle z-20 pointer-events-none"
                    style={{ left: playheadPosition }}
                  >
                    <div className="absolute -top-3 -left-2 w-5 h-5 bg-editor-teal rounded-full shadow-lg"></div>
                  </div>
                </div>
              </div>
              {/* Effects Track */}
              <div className="relative">
                <div className="flex items-center gap-4 mb-2">
                  <div className="w-20 flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded"></div>
                    <span className="text-sm font-semibold text-white">Effects</span>
                  </div>
                  <div className="text-xs text-gray-400">
                    {zoomEffects.length} effect{zoomEffects.length !== 1 ? 's' : ''}
                  </div>
                </div>
                <div className="timeline-track h-20 rounded-xl relative flex items-center" style={{ width: Math.max(timelineWidth, 800) }}>
                  {zoomEffects.map(effect => (
                    <div
                      key={effect.id}
                      className={`absolute h-14 clip-zoom border-2 border-purple-400 rounded-lg mx-1 flex flex-col justify-center px-3 cursor-move transition-all duration-200 group shadow-lg hover:shadow-xl ${
                        selectedClips.includes(effect.id) ? 'ring-2 ring-editor-purple shadow-editor-purple/25' : ''
                      } ${isDragging === effect.id ? 'opacity-75 scale-105' : ''} ${
                        trimPreview && trimPreview.clipId === effect.id && trimPreview.type === 'zoom' ? 'opacity-75' : ''
                      }`}
                      style={{ 
                        left: getEffectPosition(effect),
                        width: getEffectWidth(effect),
                        minWidth: '80px',
                        top: '12px'
                      }}
                      onMouseDown={(e) => handleZoomMouseDown(e, effect.id)}
                      onContextMenu={(e) => {
                        e.preventDefault();
                        onZoomEffectDelete(effect.id);
                      }}
                      title={`Zoom ${effect.zoomLevel}% - Right-click to delete`}
                    >
                      {/* Left resize handle for zoom effects */}
                      <div
                        className={`absolute left-0 top-0 bottom-0 w-2 bg-white/80 cursor-ew-resize transition-all duration-200 rounded-l-lg ${
                          isResizing?.clipId === effect.id && isResizing?.handle === 'start' && isResizing?.type === 'zoom'
                            ? 'opacity-100 bg-editor-purple' 
                            : 'opacity-0 group-hover:opacity-100'
                        }`}
                        onMouseDown={(e) => handleResizeMouseDown(e, effect.id, 'start', 'zoom')}
                      />
                      
                      <span className="text-sm text-white font-medium truncate pointer-events-none text-center">
                        Zoom {effect.zoomLevel}%
                      </span>
                      
                      {/* Right resize handle for zoom effects */}
                      <div
                        className={`absolute right-0 top-0 bottom-0 w-2 bg-white/80 cursor-ew-resize transition-all duration-200 rounded-r-lg ${
                          isResizing?.clipId === effect.id && isResizing?.handle === 'end' && isResizing?.type === 'zoom'
                            ? 'opacity-100 bg-editor-purple' 
                            : 'opacity-0 group-hover:opacity-100'
                        }`}
                        onMouseDown={(e) => handleResizeMouseDown(e, effect.id, 'end', 'zoom')}
                      />
                    </div>
                  ))}
                  
                  {/* Playhead */}
                  <div
                    className="absolute top-0 bottom-0 w-1 scrubber-handle z-20 pointer-events-none"
                    style={{ left: playheadPosition }}
                  >
                    <div className="absolute -top-3 -left-2 w-5 h-5 bg-editor-teal rounded-full shadow-lg"></div>
                  </div>
                </div>
              </div>
              
              {/* Audio Track */}
              <div className="relative">
                <div className="timeline-track h-20 rounded-xl relative flex items-center" style={{ width: Math.max(timelineWidth, 800) }}>
                  <span className="absolute left-4 top-2 text-sm font-semibold text-white z-10 bg-black/50 px-2 py-1 rounded">Audio</span>
                  
                  {/* Audio clips rendering */}
                  {videoClips
                    .filter(clip => {
                      const extension = clip.name.split('.').pop()?.toLowerCase();
                      return ['mp3', 'wav', 'aac', 'ogg', 'm4a'].includes(extension || '');
                    })
                    .map((clip) => (
                    <div
                      key={`audio-${clip.id}`}
                      className={`group absolute top-2 bottom-2 ${getClipColor(clip)} rounded-lg cursor-pointer transition-all duration-200 hover:brightness-110 ${
                        selectedClips.includes(clip.id) ? 'ring-2 ring-editor-teal shadow-lg' : ''
                      }`}
                      style={{
                        left: getClipPosition(clip),
                        width: getClipWidth(clip),
                        minWidth: '20px'
                      }}
                      onMouseDown={(e) => handleClipMouseDown(e, clip.id)}
                    >
                      {/* Mute button */}
                      <button
                        className="absolute top-1 right-1 w-6 h-6 bg-black/50 hover:bg-black/70 rounded flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-10"
                        onClick={(e) => {
                          e.stopPropagation();
                          onClipMute(clip.id, !clip.muted);
                        }}
                      >
                        {clip.muted ? <VolumeX className="w-3 h-3 text-white" /> : <Volume2 className="w-3 h-3 text-white" />}
                      </button>
                      
                      {/* Waveform visualization */}
                      {showWaveforms ? (
                        <WaveformCanvas
                          audioUrl={clip.url}
                          width={getClipWidth(clip)}
                          height={64}
                          color="rgba(34, 197, 94, 0.8)"
                        />
                      ) : (
                        <div className="absolute inset-0 flex items-center px-2">
                          <span className="text-white text-xs font-medium truncate">{clip.name}</span>
                        </div>
                      )}
                      
                      {/* Resize handles */}
                      <div
                        className="absolute left-0 top-0 bottom-0 w-2 cursor-ew-resize bg-white/20 hover:bg-white/40 rounded-l-lg"
                        onMouseDown={(e) => handleResizeMouseDown(e, clip.id, 'start')}
                      />
                      <div
                        className="absolute right-0 top-0 bottom-0 w-2 cursor-ew-resize bg-white/20 hover:bg-white/40 rounded-r-lg"
                        onMouseDown={(e) => handleResizeMouseDown(e, clip.id, 'end')}
                      />
                    </div>
                  ))}
                  
                  {/* Playhead */}
                  <div
                    className="absolute top-0 bottom-0 w-1 scrubber-handle z-20 pointer-events-none"
                    style={{ left: playheadPosition }}
                  >
                    <div className="absolute -top-3 -left-2 w-5 h-5 bg-editor-teal rounded-full shadow-lg"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
