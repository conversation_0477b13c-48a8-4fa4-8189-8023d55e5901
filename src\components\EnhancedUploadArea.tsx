
import React, { useState, useRef } from 'react';
import { Upload, Film, Music, Image, FileCheck, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface EnhancedUploadAreaProps {
  onFileUpload: (files: File[]) => void;
  isUploading?: boolean;
  uploadProgress?: number;
}

export const EnhancedUploadArea: React.FC<EnhancedUploadAreaProps> = ({
  onFileUpload,
  isUploading = false,
  uploadProgress = 0
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const validFiles = files.filter(file => 
      file.type.startsWith('video/') || 
      file.type.startsWith('audio/') || 
      file.type.startsWith('image/')
    );
    
    if (validFiles.length > 0) {
      setUploadedFiles(prev => [...prev, ...validFiles]);
      onFileUpload(validFiles);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setUploadedFiles(prev => [...prev, ...files]);
      onFileUpload(files);
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('video/')) return <Film className="w-6 h-6 text-editor-teal" />;
    if (file.type.startsWith('audio/')) return <Music className="w-6 h-6 text-green-400" />;
    if (file.type.startsWith('image/')) return <Image className="w-6 h-6 text-editor-purple" />;
    return <FileCheck className="w-6 h-6 text-gray-400" />;
  };

  return (
    <div className="space-y-6">
      {/* Main Upload Zone */}
      <div
        className={`upload-zone relative rounded-2xl p-12 text-center transition-all duration-300 ${
          isDragOver ? 'drag-over' : ''
        } ${isUploading ? 'pointer-events-none opacity-75' : 'cursor-pointer hover-lift'}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="video/*,audio/*,image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="space-y-6">
          {/* Animated Upload Icon */}
          <div className={`mx-auto w-20 h-20 rounded-full bg-editor-teal/20 flex items-center justify-center ${
            isDragOver ? 'animate-bounce-gentle' : 'animate-pulse-subtle'
          }`}>
            <Upload className={`w-10 h-10 text-editor-teal transition-transform duration-300 ${
              isDragOver ? 'scale-110' : ''
            }`} />
          </div>
          
          {/* Upload Text */}
          <div className="space-y-2">
            <h3 className="text-2xl font-semibold text-white">
              {isDragOver ? 'Drop your files here' : 'Drag & drop your media'}
            </h3>
            <p className="text-gray-400 text-lg">
              or <span className="text-editor-teal font-medium">browse files</span>
            </p>
            <p className="text-sm text-gray-500">
              Supports videos, audio files, and images
            </p>
          </div>
          
          {/* File Type Icons */}
          <div className="flex justify-center space-x-8 pt-4">
            <div className="flex flex-col items-center space-y-2">
              <Film className="w-8 h-8 text-editor-teal" />
              <span className="text-xs text-gray-400">Video</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <Music className="w-8 h-8 text-green-400" />
              <span className="text-xs text-gray-400">Audio</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <Image className="w-8 h-8 text-editor-purple" />
              <span className="text-xs text-gray-400">Image</span>
            </div>
          </div>
        </div>
        
        {/* Upload Progress */}
        {isUploading && (
          <div className="absolute inset-0 bg-black/50 rounded-2xl flex items-center justify-center">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 border-4 border-editor-teal/30 border-t-editor-teal rounded-full animate-spin mx-auto"></div>
              <p className="text-white font-medium">Uploading files...</p>
              <div className="w-64 h-2 bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-editor-teal to-editor-purple transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-400">{uploadProgress}% complete</p>
            </div>
          </div>
        )}
      </div>
      
      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-lg font-semibold text-white">Uploaded Files</h4>
          <div className="space-y-2">
            {uploadedFiles.map((file, index) => (
              <div 
                key={index}
                className="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-xl border border-gray-700 hover:border-editor-teal/50 transition-all duration-200 group"
              >
                {getFileIcon(file)}
                <div className="flex-1 min-w-0">
                  <p className="text-white font-medium truncate">{file.name}</p>
                  <p className="text-sm text-gray-400">
                    {(file.size / (1024 * 1024)).toFixed(2)} MB
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(index)}
                  className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-gray-400 hover:text-red-400"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
