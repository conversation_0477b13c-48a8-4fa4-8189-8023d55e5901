import React from 'react';

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
  wordsPerChunk?: number; // For pasted subtitles with chunking
}

interface SubtitleOverlayProps {
  subtitle: Subtitle | null;
  videoWidth: number;
  videoHeight: number;
  currentTime: number;
}

export const SubtitleOverlay: React.FC<SubtitleOverlayProps> = ({
  subtitle,
  videoWidth,
  videoHeight,
  currentTime
}) => {
  if (!subtitle || !videoWidth || !videoHeight) return null;

  // Calculate scale factor based on a reference resolution (e.g., 1920x1080)
  // This ensures subtitles scale appropriately with video resolution
  const referenceWidth = 1920;
  const scaleFactor = videoWidth / referenceWidth;
  const scaledFontSize = Math.max(12, subtitle.fontSize * scaleFactor);

  // Get the text to display - either full text or chunked text
  const getDisplayText = () => {
    if (!subtitle.wordsPerChunk) {
      return subtitle.text;
    }

    // For chunked subtitles, calculate which chunk to show based on current time
    const words = subtitle.text.split(/\s+/);
    const totalChunks = Math.ceil(words.length / subtitle.wordsPerChunk);
    const subtitleDuration = subtitle.endTime - subtitle.startTime;
    const chunkDuration = subtitleDuration / totalChunks;
    const relativeTime = currentTime - subtitle.startTime;
    const currentChunkIndex = Math.floor(relativeTime / chunkDuration);
    
    // Ensure we don't go out of bounds
    const chunkIndex = Math.max(0, Math.min(currentChunkIndex, totalChunks - 1));
    const startIndex = chunkIndex * subtitle.wordsPerChunk;
    const endIndex = Math.min(startIndex + subtitle.wordsPerChunk, words.length);
    
    return words.slice(startIndex, endIndex).join(' ');
  };

  const displayText = getDisplayText();

  const getPositionStyles = () => {
    const baseStyles = {
      position: 'absolute' as const,
      left: '50%',
      transform: 'translateX(-50%)',
      maxWidth: '90%',
      textAlign: 'center' as const,
      padding: `${Math.max(4, 8 * scaleFactor)}px ${Math.max(8, 16 * scaleFactor)}px`,
      borderRadius: `${Math.max(2, 4 * scaleFactor)}px`,
      fontSize: `${scaledFontSize}px`,
      fontFamily: subtitle.fontFamily,
      color: subtitle.fontColor,
      backgroundColor: subtitle.backgroundColor,
      whiteSpace: 'pre-wrap' as const,
      wordWrap: 'break-word' as const,
      lineHeight: '1.2',
      textShadow: `${Math.max(1, scaleFactor)}px ${Math.max(1, scaleFactor)}px ${Math.max(2, 2 * scaleFactor)}px rgba(0, 0, 0, 0.8)`,
      zIndex: 10,
      pointerEvents: 'none' as const
    };

    const verticalOffset = Math.max(20, 10 * scaleFactor);

    switch (subtitle.position) {
      case 'top':
        return {
          ...baseStyles,
          top: `${verticalOffset}px`
        };
      case 'center':
        return {
          ...baseStyles,
          top: '50%',
          transform: 'translate(-50%, -50%)'
        };
      case 'bottom':
      default:
        return {
          ...baseStyles,
          bottom: `${verticalOffset}px`
        };
    }
  };

  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 5
      }}
    >
      <div style={getPositionStyles()}>
        {displayText}
      </div>
    </div>
  );
};