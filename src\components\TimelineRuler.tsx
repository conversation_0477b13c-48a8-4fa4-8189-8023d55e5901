
import React from 'react';

interface TimelineRulerProps {
  totalDuration: number;
  timelineWidth: number;
  zoom: number;
  currentTime: number;
  onTimeSeek: (time: number) => void;
  showGrid?: boolean;
}

export const TimelineRuler: React.FC<TimelineRulerProps> = ({
  totalDuration,
  timelineWidth,
  zoom,
  currentTime,
  onTimeSeek,
  showGrid = true
}) => {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 100);
    return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
  };

  const handleClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickTime = (clickX / timelineWidth) * totalDuration;
    onTimeSeek(Math.max(0, Math.min(totalDuration, clickTime)));
  };

  // Calculate interval based on zoom level
  const getTimeInterval = () => {
    if (zoom <= 50) return 30; // 30 second intervals
    if (zoom <= 100) return 10; // 10 second intervals
    if (zoom <= 200) return 5;  // 5 second intervals
    if (zoom <= 400) return 1;  // 1 second intervals
    return 0.5; // 0.5 second intervals
  };

  const interval = getTimeInterval();
  const majorTicks = [];
  const minorTicks = [];

  // Generate major ticks
  for (let i = 0; i <= totalDuration; i += interval) {
    const x = (i / totalDuration) * timelineWidth;
    majorTicks.push({ time: i, x });
  }

  // Generate minor ticks (half intervals)
  if (interval >= 1) {
    const minorInterval = interval / (interval >= 10 ? 5 : 2);
    for (let i = 0; i <= totalDuration; i += minorInterval) {
      if (i % interval !== 0) { // Don't duplicate major ticks
        const x = (i / totalDuration) * timelineWidth;
        minorTicks.push({ time: i, x });
      }
    }
  }

  return (
    <div className="relative">
      {/* Ruler */}
      <div 
        className="h-8 bg-gray-750 border-b border-gray-600 relative cursor-pointer overflow-hidden"
        style={{ width: Math.max(timelineWidth, 800) }}
        onClick={handleClick}
      >
        {/* Grid lines (if enabled) */}
        {showGrid && (
          <div className="absolute inset-0 pointer-events-none">
            {majorTicks.map((tick, index) => (
              <div
                key={`grid-${index}`}
                className="absolute top-0 bottom-0 w-px bg-gray-600 opacity-30"
                style={{ left: tick.x }}
              />
            ))}
          </div>
        )}

        {/* Major ticks */}
        {majorTicks.map((tick, index) => (
          <div
            key={`major-${index}`}
            className="absolute top-0 h-full flex flex-col items-start"
            style={{ left: tick.x }}
          >
            <div className="w-px h-3 bg-gray-300" />
            <span className="text-xs text-gray-300 ml-1 mt-0.5 whitespace-nowrap">
              {formatTime(tick.time)}
            </span>
          </div>
        ))}

        {/* Minor ticks */}
        {minorTicks.map((tick, index) => (
          <div
            key={`minor-${index}`}
            className="absolute top-0"
            style={{ left: tick.x }}
          >
            <div className="w-px h-2 bg-gray-400" />
          </div>
        ))}

        {/* Current time indicator */}
        <div
          className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 pointer-events-none"
          style={{ left: (currentTime / totalDuration) * timelineWidth }}
        >
          <div className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full" />
        </div>
      </div>
    </div>
  );
};
