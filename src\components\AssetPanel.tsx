import React, { useState } from 'react';
import { Upload, <PERSON>Vide<PERSON>, <PERSON>, <PERSON>rk<PERSON>, ArrowRightLeft, Shapes } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';

// Utility function to format time
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
  sourceStartTime?: number;
  sourceEndTime?: number;
  muted?: boolean;
}

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
  wordsPerChunk?: number;
}

interface AssetPanelProps {
  videoClips: VideoClip[];
  selectedClips: string[];
  subtitles: Subtitle[];
  selectedSubtitles: string[];
  currentTime: number;
  isUploading: boolean;
  uploadProgress: number;
  onClipSelect: (clipId: string, isMultiSelect?: boolean) => void;
  onUploadClick: () => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
  // Subtitle functions
  onAddSubtitle: () => void;
  onUpdateSubtitle: (id: string, updates: Partial<Subtitle>) => void;
  onDeleteSubtitle: (id: string) => void;
  onSelectSubtitle: (id: string) => void;
  onImportSubtitles?: (subtitles: Subtitle[]) => void;
  onDeleteAllSubtitles?: () => void;
  onAddPastedSubtitle?: (text: string, wordsPerChunk: number) => void;
  getTimelineDuration: () => number;
}

const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

export const AssetPanel: React.FC<AssetPanelProps> = ({
  videoClips,
  selectedClips,
  subtitles,
  selectedSubtitles,
  currentTime,
  isUploading,
  uploadProgress,
  onClipSelect,
  onUploadClick,
  onDragOver,
  onDrop,
  onAddSubtitle,
  onUpdateSubtitle,
  onDeleteSubtitle,
  onSelectSubtitle,
  onImportSubtitles,
  onDeleteAllSubtitles,
  onAddPastedSubtitle,
  getTimelineDuration
}) => {
  const [activeTab, setActiveTab] = useState('files');

  return (
    <div className="h-full bg-gray-800 border-r border-gray-700 flex flex-col">
      <div className="p-4 border-b border-gray-700">
        <h3 className="font-semibold text-gray-200 mb-4">Assets</h3>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5 bg-gray-700">
            <TabsTrigger value="files" className="text-xs p-2">
              <FileVideo className="w-4 h-4" />
            </TabsTrigger>
            <TabsTrigger value="text" className="text-xs p-2 relative">
              <Type className="w-4 h-4" />
              {subtitles.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-purple-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {subtitles.length}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="effects" className="text-xs p-2">
              <Sparkles className="w-4 h-4" />
            </TabsTrigger>
            <TabsTrigger value="transitions" className="text-xs p-2">
              <ArrowRightLeft className="w-4 h-4" />
            </TabsTrigger>
            <TabsTrigger value="elements" className="text-xs p-2">
              <Shapes className="w-4 h-4" />
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="files" className="mt-4 space-y-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-300">Project Files</h4>
              
              {/* Upload Area */}
              <div 
                className="border-2 border-dashed border-gray-600 rounded-lg p-4 text-center cursor-pointer hover:border-gray-500 transition-colors"
                onDragOver={onDragOver}
                onDrop={onDrop}
                onClick={onUploadClick}
              >
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-400">Drop video files here or click to upload</p>
                <p className="text-xs text-gray-500 mt-1">Supports MP4, MOV, AVI, WebM</p>
              </div>
              
              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <div className="text-sm text-gray-300">Uploading...</div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              )}
              
              {/* Video Files List */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {videoClips.map(clip => (
                  <Card
                    key={clip.id}
                    className={`p-3 cursor-pointer transition-colors ${
                      selectedClips.includes(clip.id)
                        ? 'bg-blue-600 border-blue-500' 
                        : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                    }`}
                    onClick={(e) => onClipSelect(clip.id, e.ctrlKey || e.metaKey)}
                  >
                    <div className="flex items-center space-x-3">
                      <FileVideo className="w-4 h-4 text-gray-400 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-white truncate">{clip.name}</div>
                        <div className="text-xs text-gray-400">
                          {formatTime(clip.endTime - clip.startTime)} duration
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
                
                {videoClips.length === 0 && !isUploading && (
                  <div className="text-center text-gray-400 py-8">
                    <FileVideo className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No video files yet</p>
                    <p className="text-xs">Upload some videos to get started</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="text" className="mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-300">Text & Subtitles</h4>

              {/* Subtitle Creation Tools */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-400">Create Subtitles</span>
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={onAddSubtitle}
                      size="sm"
                      className="flex items-center gap-1 bg-purple-600 hover:bg-purple-700"
                    >
                      <Type className="w-3 h-3" />
                      Add
                    </Button>
                  </div>
                </div>

                {/* Import Tools */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={() => onImportSubtitles && onImportSubtitles([])}
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-1 text-xs"
                    >
                      <Upload className="w-3 h-3" />
                      Import
                    </Button>
                    {subtitles.length > 0 && onDeleteAllSubtitles && (
                      <Button
                        onClick={onDeleteAllSubtitles}
                        size="sm"
                        variant="outline"
                        className="flex items-center gap-1 text-xs text-red-400 hover:text-red-300"
                      >
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>

                {/* Paste Text Section */}
                <div className="border border-gray-600 rounded-lg p-3 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-300">Paste Text</span>
                    <span className="text-xs text-gray-500">Bulk create</span>
                  </div>

                  <textarea
                    placeholder="Paste your text here to create multiple subtitles..."
                    className="w-full h-20 text-xs bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white placeholder-gray-400 resize-none"
                    onChange={(e) => {
                      // Store the text for processing
                      const text = e.target.value;
                      if (text.trim() && onAddPastedSubtitle) {
                        // Auto-process when text is pasted (you can modify this behavior)
                      }
                    }}
                  />

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-400">Words per subtitle:</span>
                    <select className="text-xs bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white">
                      <option value="2">2</option>
                      <option value="3">3</option>
                      <option value="4">4</option>
                      <option value="5">5</option>
                    </select>
                  </div>

                  <Button
                    onClick={() => {
                      const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
                      const select = document.querySelector('select') as HTMLSelectElement;
                      if (textarea?.value.trim() && onAddPastedSubtitle) {
                        onAddPastedSubtitle(textarea.value, parseInt(select.value));
                        textarea.value = '';
                      }
                    }}
                    size="sm"
                    className="w-full text-xs"
                  >
                    Create Subtitles
                  </Button>
                </div>
              </div>

              {/* Subtitle List (Read-only) */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-400">Existing Subtitles</span>
                  <span className="text-xs text-gray-500">{subtitles.length} total</span>
                </div>

                <div className="space-y-1 max-h-40 overflow-y-auto">
                  {subtitles.map((subtitle) => (
                    <div
                      key={subtitle.id}
                      className={`p-2 border rounded cursor-pointer transition-colors text-xs ${
                        selectedSubtitles.includes(subtitle.id)
                          ? 'border-purple-500 bg-purple-500/20'
                          : 'border-gray-600 hover:border-gray-500'
                      }`}
                      onClick={() => onSelectSubtitle(subtitle.id)}
                    >
                      <div className="text-white font-medium truncate">{subtitle.text}</div>
                      <div className="text-gray-400 text-xs">
                        {formatTime(subtitle.startTime)} → {formatTime(subtitle.endTime)}
                      </div>
                    </div>
                  ))}

                  {subtitles.length === 0 && (
                    <div className="text-center text-gray-500 py-4">
                      <Type className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-xs">No subtitles yet</p>
                      <p className="text-xs">Add some to get started</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="effects" className="mt-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-300">Effects</h4>
              <div className="text-center text-gray-400 py-8">
                <Sparkles className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Effects coming soon</p>
                <p className="text-xs">Video filters and color LUTs</p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="transitions" className="mt-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-300">Transitions</h4>
              <div className="text-center text-gray-400 py-8">
                <ArrowRightLeft className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Transitions coming soon</p>
                <p className="text-xs">Wipes, dissolves, and more</p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="elements" className="mt-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-300">Elements</h4>
              <div className="text-center text-gray-400 py-8">
                <Shapes className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Elements coming soon</p>
                <p className="text-xs">Stickers, shapes, and overlays</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
