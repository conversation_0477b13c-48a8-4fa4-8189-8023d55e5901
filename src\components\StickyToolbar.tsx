
import React from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { 
  Save, 
  Download, 
  Undo, 
  Redo, 
  ZoomIn, 
  ZoomOut, 
  Gauge,
  Clock,
  Settings
} from 'lucide-react';

interface StickyToolbarProps {
  onSave: () => void;
  onExport: () => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  zoom: number;
  onZoomChange: (zoom: number) => void;
  playbackSpeed: number;
  onPlaybackSpeedChange: (speed: number) => void;
  isAutoSaving?: boolean;
}

export const StickyToolbar: React.FC<StickyToolbarProps> = ({
  onSave,
  onExport,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  zoom,
  onZoomChange,
  playbackSpeed,
  onPlaybackSpeedChange,
  isAutoSaving = false
}) => {
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="glass-morphism rounded-2xl p-4 shadow-2xl border border-white/10">
          <div className="flex items-center justify-between">
            {/* Left Section - File Actions */}
            <div className="flex items-center space-x-3">
              <Button
                onClick={onSave}
                className={`bg-editor-teal hover:bg-editor-teal/90 text-white font-medium px-6 interactive-element ${
                  isAutoSaving ? 'animate-pulse-subtle' : ''
                }`}
              >
                <Save className="w-4 h-4 mr-2" />
                {isAutoSaving ? 'Auto-saving...' : 'Save'}
              </Button>
              
              <Button
                onClick={onExport}
                className="bg-editor-purple hover:bg-editor-purple/90 text-white font-medium px-6 interactive-element"
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
            
            {/* Center Section - Timeline Controls */}
            <div className="flex items-center space-x-6">
              {/* Undo/Redo */}
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onUndo}
                  disabled={!canUndo}
                  className="text-white hover:text-editor-teal disabled:opacity-50 interactive-element"
                >
                  <Undo className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRedo}
                  disabled={!canRedo}
                  className="text-white hover:text-editor-teal disabled:opacity-50 interactive-element"
                >
                  <Redo className="w-4 h-4" />
                </Button>
              </div>
              
              {/* Zoom Control */}
              <div className="flex items-center space-x-3 min-w-[200px]">
                <ZoomOut className="w-4 h-4 text-gray-400" />
                <div className="flex-1">
                  <Slider
                    value={[zoom]}
                    onValueChange={(value) => onZoomChange(value[0])}
                    max={400}
                    min={25}
                    step={25}
                    className="w-full"
                  />
                </div>
                <ZoomIn className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-300 min-w-[40px] text-center font-medium">
                  {zoom}%
                </span>
              </div>
              
              {/* Playback Speed Control */}
              <div className="flex items-center space-x-3 min-w-[180px]">
                <Clock className="w-4 h-4 text-gray-400" />
                <div className="flex-1">
                  <Slider
                    value={[playbackSpeed]}
                    onValueChange={(value) => onPlaybackSpeedChange(value[0])}
                    max={3}
                    min={0.25}
                    step={0.25}
                    className="w-full"
                  />
                </div>
                <Gauge className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-300 min-w-[40px] text-center font-medium">
                  {playbackSpeed}x
                </span>
              </div>
            </div>
            
            {/* Right Section - Settings */}
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white interactive-element"
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
