
import { useEffect, useCallback } from 'react';

interface KeyboardShortcuts {
  onPlay?: () => void;
  onCut?: () => void;
  onCopy?: () => void;
  onPaste?: () => void;
  onDelete?: () => void;
  onDeleteSubtitle?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onSave?: () => void;
  onSelectAll?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
}

export const useKeyboardShortcuts = (shortcuts: KeyboardShortcuts) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const { ctrlKey, metaKey, shiftKey, key } = event;
    const isModifier = ctrlKey || metaKey;

    // Prevent shortcuts when typing in inputs
    if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
      return;
    }

    switch (key.toLowerCase()) {
      case ' ':
        event.preventDefault();
        shortcuts.onPlay?.();
        break;
      case 'x':
        if (isModifier) {
          event.preventDefault();
          shortcuts.onCut?.();
        }
        break;
      case 'c':
        if (isModifier) {
          event.preventDefault();
          shortcuts.onCopy?.();
        }
        break;
      case 'v':
        if (isModifier) {
          event.preventDefault();
          shortcuts.onPaste?.();
        }
        break;
      case 'delete':
      case 'backspace':
        if (!isModifier) {
          event.preventDefault();
          // Check if Shift is held to delete subtitles specifically
          if (shiftKey) {
            shortcuts.onDeleteSubtitle?.();
          } else {
            shortcuts.onDelete?.();
          }
        }
        break;
      case 'z':
        if (isModifier) {
          event.preventDefault();
          if (shiftKey) {
            shortcuts.onRedo?.();
          } else {
            shortcuts.onUndo?.();
          }
        }
        break;
      case 'y':
        if (isModifier) {
          event.preventDefault();
          shortcuts.onRedo?.();
        }
        break;
      case 's':
        if (isModifier) {
          event.preventDefault();
          shortcuts.onSave?.();
        }
        break;
      case 'a':
        if (isModifier) {
          event.preventDefault();
          shortcuts.onSelectAll?.();
        }
        break;
      case '=':
      case '+':
        if (isModifier) {
          event.preventDefault();
          shortcuts.onZoomIn?.();
        }
        break;
      case '-':
        if (isModifier) {
          event.preventDefault();
          shortcuts.onZoomOut?.();
        }
        break;
    }
  }, [shortcuts]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);
};
